/**app.wxss**/
@import "./lib/vant-weapp/common/index.wxss";
@import "./static/css/vant.wxss";
@import "./static/css/font.wxss";
@import "./static/css/flex.wxss";
@import "./static/css/layout.wxss";
@import "./static/css/button.wxss";
/** vant-weapp 推荐字体 **/
page {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica,
    Segoe UI, Arial, Roboto, "PingFang SC", "Hiragino Sans GB",
    "Microsoft Yahei", sans-serif;
  --themeColor: #2893ff;
  --labelColor: #38bf87;
  --redColor: #f06454;
  --whiteColor: #ffffff;
  --btnThemeColor: #2893ff;
  --btnRadius: 8rpx;
  --btnShadow: 0rpx 4rpx 8rpx 0rpx rgba(40, 211, 230, 0.24);
  --bgColor: #f7f7f7;
	--containerRadius:8rpx;
}
::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}
view,
image,
text,
navigator {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

view,
text,
input {
  /* font-size: 24rpx; */
  /* font-family: PingFangSC-Light, helvetica, 'Heiti SC'; */
  font-family: PingFangSC-Regular, sans-serif;
}
.fontMedium {
  font-family: PingFangSC-Medium, sans-serif;
}
.fontScMedium {
  font-family: PingFang-SC-Medium, PingFang-SC;
}
.fontDIN {
  font-family: DIN-Regular;
}

.cfff {
  color: #fff;
}
.c333 {
  color: #333;
}
.c666 {
  color: #666;
}
.c444 {
  color: #444;
}
.c999 {
  color: #999;
}

.color-danger {
  color: #f06454;
}
.color-primary {
  color: #2893ff;
}
.color-success {
  color: #38bf87;
}

.bg-color-white {
  background: var(--whiteColor);
}
.bg-color-primary {
  background: var(--themeColor);
}
.bg-color-gray-light {
  background: var(--bgColor);
}
.bg-color-blue-light {
	background-color: #F2F6FF;
}
.container-radius{
	border-radius: var(--containerRadius);
}

.dn {
  display: none;
}
.di {
  display: inline;
}
.db {
  display: block;
}
.w100 {
  width: 100%;
}
.h100 {
  height: 100%;
}
.dib {
  display: inline-block;
}
.lh14 {
  line-height: 14rpx;
}
.lh16 {
  line-height: 16rpx;
}
.lh18 {
  line-height: 18rpx;
}
.lh20 {
  line-height: 20rpx;
}
.lh22 {
  line-height: 22rpx;
}
.lh24 {
  line-height: 24rpx;
}
.lh28 {
  line-height: 28rpx;
}
.lh30 {
  line-height: 30rpx;
}
.lh32 {
  line-height: 32rpx;
}
.lh34 {
  line-height: 34rpx;
}
.lh36 {
  line-height: 36rpx;
}
.lh40 {
  line-height: 40rpx;
}

.bt1 {
  border-top: 1rpx solid #eeeeee;
}
.bb1 {
  border-bottom: 1rpx solid #eeeeee;
}
.bl1 {
  border-left: 1rpx solid #f6f8fc;
}
.br1 {
  border-right: 1rpx solid #f6f8fc;
}
.bt10 {
  border-top: 10rpx solid #f6f8fc;
}
.bb10 {
  border-bottom: 10rpx solid #f6f8fc;
}

.n {
  font-weight: normal;
  font-style: normal;
}
.b {
  font-weight: bold;
}
.b500 {
  font-weight: 500;
}
.i {
  font-style: italic;
}
.tc {
  text-align: center;
}
.tr {
  text-align: right;
}
.tl {
  text-align: left;
}
.tj,
.th_tj {
  text-align: justify;
}
.th_tj:after {
  content: "";
  display: inline-block;
  width: 100%;
  height: 1rpx;
}
.tdl {
  text-decoration: underline;
}
.lt-1 {
  letter-spacing: -1rpx;
}
.lt0 {
  letter-spacing: 0;
}
.lt1 {
  letter-spacing: 1rpx;
}
.nowrap {
  white-space: nowrap;
}
.bk {
  word-wrap: break-word;
}
.vm {
  vertical-align: middle;
}
.vb {
  vertical-align: bottom;
}
.vt {
  vertical-align: top;
}
.vn {
  vertical-align: -5rpx;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.cl {
  clear: both;
}
.rel {
  position: relative;
}
.abs,
.cm {
  position: absolute;
}
.fixed {
  position: fixed;
}
.zx1 {
  z-index: 1;
}
.zx2 {
  z-index: 2;
}
.zx8 {
  z-index: 8;
}
.zx9 {
  z-index: 9;
}
.zx99 {
  z-index: 99;
}
.zx999 {
  z-index: 999;
}
.cm {
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.t0 {
  top: 0;
}
.t50 {
  top: 50%;
}
.b0 {
  bottom: 0;
}
.l0 {
  left: 0;
}
.l50 {
  left: 50%;
}
.r0 {
  right: 0;
}
.ovh {
  overflow: hidden;
}
.ova {
  overflow: auto;
}
.vh {
  visibility: hidden;
}
.vv {
  visibility: visible;
}
.o0 {
  opacity: 0;
}
.clearfix,
.fix {
  zoom: 1;
}
.clearfix:after,
.fix:after {
  display: table;
  content: "";
  clear: both;
}
.ell {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.ell_more {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
.ell_1 {
  -webkit-line-clamp: 1;
}
.ell_2 {
  -webkit-line-clamp: 2;
}
.ell_3 {
  -webkit-line-clamp: 3;
}
.middle {
  display: inline-block;
  width: 0;
  height: 100%;
  vertical-align: middle;
}
.trans {
  transition: all 250ms;
}
.disabled {
  opacity: 0.4;
  filter: alpha(opacity=40);
  cursor: default;
  -ms-pointer-events: none;
  pointer-events: none;
}

.container {
  min-height: 100%;
}

.imgBlock {
  display: block;
  width: 100%;
  height: 100%;
}
.no_msg_box {
  width: 100%;
  position: absolute;
  top: 394rpx;
}
.no_msg {
  width: 382rpx;
  height: 316rpx;
}
/* 箭头 */
.rigth-icon {
  position: relative;
  font: normal normal normal 14px/1 vant-icon;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

.rigth-icon:before {
  content: "\F00A";
}

.container .van-popup {
  border-radius: 10px;
  border-radius: var(--popup-round-border-radius, 10px);
  position: fixed;
  box-sizing: border-box;
  max-height: 100%;
  overflow-y: auto;
  transition-timing-function: ease;
  -webkit-animation: ease both;
  animation: ease both;
  -webkit-overflow-scrolling: touch;
  background-color: #fff;
  background-color: var(--popup-background-color, #fff);
}
.w35{
	width: 35rpx;
}
.w40{
	width: 40rpx;
}
.w60{
	width: 60rpx;
}
.h35{
	height: 35rpx;
}
.h40{
	height: 40rpx;
}
.h60{
	height: 60rpx;
}

/* 单选按钮 */
radio .wx-radio-input {
  border-radius: 50%;
  height: 36rpx;
  width: 36rpx;
  /* margin-top: -4rpx; */
  box-sizing: border-box;
}
radio .wx-radio-input.wx-radio-input-checked {
  width: 36rpx;
  height: 36rpx;
  background-color: #52a7ff;
  border: 1rpx solid #52a7ff;
  box-sizing: border-box;
  text-align: center;
}
radio .wx-radio-input.wx-radio-input-checked::before {
  font-size: 24rpx;
}
