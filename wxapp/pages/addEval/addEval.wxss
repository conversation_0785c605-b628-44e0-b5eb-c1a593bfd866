/* pages/addEval/addEval.wxss */
page{
  background: #F7F7F7;
}
.container{
  padding-bottom: 276rpx;
}
.bj{
  position: fixed;
  left: 0;
  top: 0;
  width: 750rpx;
  height: 300rpx;
}
.doctorInfo{
  border-radius: 8rpx;
}
.doctorInfo .img{
   width: 112rpx;
   height: 112rpx;
   border-radius: 50%;
   overflow: hidden;
   background: #F7F7F7;
}
.lh44{
  line-height: 44rpx;
}
.evalBox{
  border-radius: 8rpx;
  margin-top: 32rpx;
}
.evalBox .title{
  position: relative;
  padding-left: 20rpx;
}
.evalBox .title::after{
  position: absolute;
  width: 12rpx;
  height: 28rpx;
  border-radius: 6rpx;
  background: var(--themeColor);
  content: '';
  left: 0;
  top: 50%;
  margin-top: -14rpx;
}
.tag text{
  display: inline-block;
  height: 52rpx;
  line-height: 50rpx;
  text-align: center;
  border: 1rpx solid #CCCCCC;
  font-size: 28rpx;
  color: #999;
  border-radius: 4rpx;
  margin-right: 30rpx;
  padding: 0 22rpx;
  margin-top: 32rpx;
}
/* .tag text:nth-child(3n){
  margin-right: 0rpx;
} */
.tag text.cur{
  border-color: var(--themeColor);
  background: var(--themeColor);
  color: #fff;
}
.textArea{
  width: 100%;
  background: #FAFAFA;
  border-radius: 8rpx;
  margin-top: 32rpx;
}
.textArea textarea{
  display: block;
  width: 100%;
  height: 302rpx;
  box-sizing: border-box;
  padding: 20rpx;
  font-size: 28rpx;
}
.textNum{
  padding-right: 12rpx;
  padding-bottom: 12rpx;
}
.hideName{
  padding-top: 24rpx;
}
.radioCustom image{
  display: inline-block;
  width:40rpx;height: 40rpx;
  vertical-align: top;
  margin-top: -2rpx;
}
.radioCustom .van-checkbox__label{
  margin-left: 0rpx;
  line-height: 40rpx;
}
.confir{
  z-index: 1000;
}