// pages/addEval/addEval.js
var api = require('../../config/api.js')
var util = require('../../utils/util')
// const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    doctorInfo: null,
    type: null,
    consultType: null,
    doctorId: null,
    consultSessionId: null,
    imgObject: {
      background: api.ImgUrl + 'images/bg_evaluate.png',
      unselected: api.ImgUrl + 'images/ic_evaluate_unselected.png',
      selected: api.ImgUrl + 'images/ic_evaluate_selected.png'
    },
    isBack: true,
    navTitle: '',
    value: 3,
    hideName: false, //匿名
    agree: false,
    textNum: 0,
    content: '',
    tags: [{ name: '未给诊断', check: false }, { name: '回复不及时', check: false }, { name: '亲切有耐心', check: false }, { name: '效果不明显', check: false }, { name: '建议有帮助', check: false }, { name: '没解决问题', check: false }],
    exist: false,
    commentDetail: null,
    pageLevel: false //页面层级 有上一级true 没有false
  },
  onChange(event) {
    let tags
    if (event.detail <= 2 && event.detail !== this.data.value) {
      tags = [{ name: '回复太慢', check: false }, { name: '未给实质性建议', check: false }, { name: '态度冷漠', check: false }, { name: '答非所问', check: false }, { name: '未解决问题', check: false }, { name: '让我去医院', check: false }]
    } else if (event.detail <= 4 && event.detail !== this.data.value) {
      tags = [{ name: '未给诊断', check: false }, { name: '回复不及时', check: false }, { name: '亲切有耐心', check: false }, { name: '效果不明显', check: false }, { name: '建议有帮助', check: false }, { name: '没解决问题', check: false }]
    } else if (event.detail === 5 && event.detail !== this.data.value) {
      tags = [{ name: '回复及时', check: false }, { name: '亲切有耐心', check: false }, { name: '非常专业', check: false }, { name: '不乱开药', check: false }, { name: '建议有帮助', check: false }, { name: '疗效明显', check: false }]
    }
    this.setData({
      value: event.detail,
      tags: tags
    })
  },
  hideNameFun(e) {
    this.setData({
      hideName: e.detail
    })
  },
  agreeFun(e) {
    this.setData({
      agree: e.detail
    })
  },
  textContent(e) {
    this.setData({
      content: e.detail.value,
      textNum: e.detail.cursor
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      navTitle: '评价',
      consultType: options.consultType,
      type: options.type,
      doctorId: options.doctorId,
      consultSessionId: options.consultSessionId
    })
  },
  /**
	 * 获取医生信息
	 */
  getDoctorDetail() {
    const params = {}
    params.doctorId = this.data.doctorId
    util.request(api.doctorSimple, params, 'GET').then(d => {
      var cdata = d.data
      if (cdata.code === 0) {
        this.setData({
          doctorInfo: cdata.data
        })
      } else {
        util.showToast({
          title: cdata.msg
        })
      }
    }, c => {
      wx.hideLoading()
    })
  },
  // 判断是否可以多次提交
  async valid() {
    const params = {
      type: this.data.type,
      consultType: this.data.consultType,
      consultSessionId: this.data.consultSessionId
    }
    try {
      const { data } = await util.request(api.valid, params, 'get')
      if (data.code === 0) {
        if (data.data.exist) {
          console.log(data.data, 109)
          this.setData({
            exist: data.data.exist,
            value: data.data.commentDetail.starLevel,
            content: data.data.commentDetail.commentContent
          })
          console.log(this.data.exist, 109)
        }
      } else {
        util.showToast({
          title: data.msg
        })
      }
    } catch (error) {
      throw new Error(error)
    }
  },
  async submitComment() {
    const params = {}
    params.type = this.data.consultType
    params.doctorId = this.data.doctorId
    params.content = this.data.content
    params.starsLevel = this.data.value
    params.wac = this.data.hideName ? 2 : 1
    params.sessionId = this.data.consultSessionId
    util.request(api.submitComment, params, 'POST', 2).then(d => {
      var cdata = d.data
      if (cdata.code === 0) {
        util.showToast({
          title: '评价成功',
          icon: 'success',
          success: () => {
            setTimeout(() => {
              if (this.data.type == 2) {
                wx.navigateBack({
                  delta: 1
                })
              } else {
                wx.switchTab({
                  url: '/pages/home/<USER>'
                })
              }
            }, 1000)
          }
        })
      } else {
        util.showToast({
          title: cdata.msg
        })
      }
    }, c => {
      wx.hideLoading()
    })
  },
  submit() {
    this.submitComment()
  },
  checkTag(e) {
    const index = e.currentTarget.dataset.index
    const name = e.currentTarget.dataset.name
    const check = e.currentTarget.dataset.check
    if (this.data.content.length + name.length > 200) {
      return false
    }
    if (!check) {
      this.data.content = this.data.content ? this.data.content + `,${name}` : this.data.content + `${name}`
    }
    this.setData({
      [`tags[${index}].check`]: !check,
      content: this.data.content
    })
  },
  goPage(e) {
    const path = e.currentTarget.dataset.path
    const type = e.currentTarget.dataset.type
    wx.navigateTo({
      url: `${path}?type=${type}`
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */

  onShow: function() {
    var that = this
    that.getDoctorDetail()
    that.valid()
  },
  touchMove() {
    util.verifyPageMessageUpdate()
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function() {

  // }
})
