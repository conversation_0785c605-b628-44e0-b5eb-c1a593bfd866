const api = require('../../config/api')
import Dialog from '../../lib/vant-weapp/dialog/dialog'

Page({
  data: {
    ic_history_deleted: api.ImgUrl + 'images/<EMAIL>',
    historyList: []
  },
  deleteHistroy() {
    Dialog.confirm({
      title: '提示',
      message: '确认清空全部搜索记录？'
    }).then(() => {
      wx.removeStorageSync('searchHistory')
      this.setData({
        historyList: []
      })
    }) .catch(() => {
      // on cancel
    })
  },
  handleClick(e) {
    wx.navigateTo({
      url: '/pages/searchMerchandiseResult/index?text=' + e.currentTarget.dataset.item
    })
  },
  getHistory() {
    const searchHistory = wx.getStorageSync('searchHistory') || []
    this.setData({
      historyList: searchHistory
    })
  },
  onLoad: function(options) {
    this.getHistory()
  },
  onShow: function() {
    this.getHistory()
  }
})
