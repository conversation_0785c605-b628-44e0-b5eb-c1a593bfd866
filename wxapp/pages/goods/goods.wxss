
page{
  background: #F8F8F8;
}
.page-section swiper{
 height: 640rpx;
}
.page-section swiper image{
  height: 640rpx;
}
.goods-name-wrap{
  background-color: #fff;
  margin-bottom: 20rpx;
  border-top: 1rpx solid #eee;
}
.goods-name{
  padding-top: 28rpx;
  font-size: 32rpx;
  margin-left: 28rpx;
  font-weight: 700;
}
.goods-name view{
 margin-right: 16rpx;
 display: inline-block;
}
.goods-name-wrap .price-warp{
  display: flex;
  margin-left: 28rpx;
  color: rgba(255, 0, 0, 1);
  padding-bottom: 20rpx;
  font-size: 34rpx;
}
.swiper-item-inner{
  position: relative;
}
.swiper {
  position: relative;
}
.swiper-item-num{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 40rpx;
  background: rgba(204, 204, 204, 1);
  border-radius: 4rpx;
  color: #fff;
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  text-align: center;
  font-size: 26rpx;
}
.price-warp .unit{
  margin-right: 10rpx;
}
.price-warp .price{
  font-weight: 500;
}

.goods-detail{
  background-color: #fff;
  padding: 24rpx 28rpx;
  box-sizing: border-box;
  padding-bottom: 168rpx;
}
.goods-detail .goods-detail-title{
  font-size: 32rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #333333;
}
.goods-detail-content{
  border-top:1rpx solid #EEEEEE;
  margin-top: 16rpx;
}
.goods-detail-content .content-item{
  display: flex;
  border-bottom:1rpx solid #EEEEEE;
  /* padding: 20rpx 14rpx; */
}
.goods-detail-content .content-item .label{
  font-size: 26rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #999999;
  width: 200rpx;
  border-left:1rpx solid #EEEEEE;
  border-right:1rpx solid #EEEEEE;
  padding: 20rpx 14rpx;
}
.goods-detail-content .content-item .content{
  font-size: 26rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 36rpx;
  flex: 1;
  border-right:1rpx solid #EEEEEE;
  padding: 20rpx 14rpx;
}
.footer{
  width: 100%;
  background: #FFFFFF;
  position: fixed;
  bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  /* border-top: 1px solid #EEEEEE; */
  box-shadow: 0rpx 0rpx 20rpx 0rpx rgba(0,0,0,0.2)
}
.footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 98rpx;
  padding-left: 60rpx;
  padding-right: 20rpx;
}

.footer .left{
  font-size: 18rpx;
}
.footer .left .van-icon{
  font-size: 48rpx;
}
.footer .right{
  display: flex;
}
.footer .right .btn{
  width: 216rpx;
  height: 72rpx;
  line-height: 72rpx;
  background: #FF9B3A;
  border-radius: 38rpx;
  color: #fff;
  font-size: 28rpx;
  text-align: center;
}
.footer .right .cart-btn{
  background: #FF9B3A;
  margin-right: 12rpx;
}
.footer .right .buy-btn{
  background: #367DFF;
}
.footer .right .no-btn{
  background: #BBBBBB;
}

/* 弹框 */
.choose-sku{
  padding: 28rpx;
}
.choose-sku .top{
  display: flex;
}
.choose-sku .top image{
  width: 180rpx;
  height: auto;
  /* height: 180rpx; */
  background: #F8F8F8;
  border-radius: 8rpx;
  margin-right: 20rpx;
}
.choose-sku .top .detail{
  margin-right: 74rpx;
  /* display: flex; */
}
.choose-sku .top .price{
  color: #F05542;
  margin-top: 8rpx;
}
.choose-sku .top .detail-inner view{
  display: inline-block;
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 40rpx;
  margin-right: 10rpx;
}
.choose-sku .center{
  margin-top: 60rpx;
}
.choose-sku .center .sku-warp{
  display: flex;
  margin-top: 20rpx;
  margin-bottom: 50rpx;
}
.choose-sku .center .sku-item{
  width: 144rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  background: #F5F5F5;
  border-radius: 8rpx;
  margin-right: 20rpx;
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}
.choose-sku .center .sku-item-active{
  background: #ECF3FF;
  font-weight: 500;
  color: #367DFF;
}
.choose-sku .bottom{
  display: flex;
  justify-content: space-between;
  margin-bottom: 70rpx;
}
.choose-sku .btn-warp .btn{
  height: 80rpx;
  line-height: 80rpx;
  background: #367DFF;
  border-radius: 8rpx;
  width: 100%;
  text-align: center;
  color: #fff;
}
.section-title {
  font-size: 28rpx !important;
  font-weight: 700;
}
