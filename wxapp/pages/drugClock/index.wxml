<navbar isBack="{{isBack}}" backgroundColor='#fff' navTitle="用药提醒"></navbar>
<view class="container bg-color-gray-light">
	<view class="f24 tc p20 color-primary bg-color-blue-light">
		根据处方内药品用法用量，建议用药提醒频次为每日{{drugData.reminds.length}}次
	</view>
	<view class="drug container-radius bg-color-white p20 m20">
		<view class="c333 f32 b">
			药品信息
		</view>
		<view class="drug-item pt20 {{index != drugData.items.length-1 ? 'bb1 pb20' :''}}" wx:for='{{drugData.items}}' wx:key="index">
			<view class="flex_lr">
				<view class="c333 f30 flex1">
				{{item.name}}
				</view>
				<view class="c333 f30 ml60">
					x{{item.quantity}}{{item.quantityUnit}}
				</view>
			</view>
			<view class="c999 f24 mt5">
				用法：{{item.usages}}
			</view>
		</view>
	</view>
	<view class="clock container-radius bg-color-white p20 m20">
		<view class="c333 f32 b mb30">
			用药提醒
		</view>
		<view class="clock-item flex_m {{index != drugData.reminds.length-1 ? 'mb20' :''}}" wx:for='{{drugData.reminds}}' wx:key='index'>
			<van-checkbox value="{{ item.valid }}" data-index='{{index}}' icon-size='18' bind:change="onClockChange">
			</van-checkbox>
			<view class="clock-item-right flex_m w100 ml40" data-index='{{index}}' data-minite='{{item.minite}}' data-hour='{{item.hour}}' bind:tap="handleShowSelect">
				<view class="c666 f28 flex1 flex_c_m h100">
					第{{index+1}}次提醒
				</view>
				<view style="margin-right:7rpx"
					class="c333 f28 flex1 bg-color-white clock-item-select container-radius flex_c_m rel">
					{{item.hour < 10 ? '0'+item.hour : item.hour}}:{{item.minite < 10 ? '0'+item.minite : item.minite}}
					<image src="../../static/images/ic_open.png" class="clock-item-image"></image>
				</view>
			</view>
		</view>
	</view>
</view>
<view class="fixed-button flex_m bt1 pl30 pr30">
	<view class="fixed-btn-close flex_c_m c999 f32" data-type='2' bindtap='handleSetClock'>
		关闭提醒
	</view>
	<view class="fixed-btn-save flex_c_m cfff f32 ml30" data-type='1' bindtap='handleSetClock'>
		保存开启
	</view>
</view>
<van-popup close-on-click-overlay='{{true}}' show="{{ timeShow }}" bind:close="handleSetClockTime" position='bottom'>
	<van-datetime-picker type="time" title='选择用药时间' bind:cancel='handleSetClockTime' bind:confirm='handleConfirmTime'
		value="{{ currentDate }}" min-hour="{{ minHour }}" max-hour="{{ maxHour }}" />
</van-popup>