const api = require('../../config/api.js')
const util = require('../../utils/util')
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    drugData: {},
    isBack: true,
    timeShow: false,
    recomId: null,
    current: null,
    currentDate: null,
    minHour: 8,
    maxHour: 20
  },

  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    this.setData({
      recomId: options.recomId
    })
  },
  onShow() {
    this.getDetail()
  },
  async getDetail() {
    util.showLoading({
      title: 'loading',
      mask: true
    })
    try {
      const {
        data
      } = await util.request(api.drugremind, {
        recomId: this.data.recomId
      }, 'get')
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
      this.setData({
        drugData: data.data
      })
    } catch (error) {
      throw new Error(error)
    }
    util.hideLoading()
  },
  onClockChange(e) {
    const {
      index
    } = e.currentTarget.dataset
    this.setData({
      [`drugData.reminds[${index}].valid`]: e.detail
    })
  },
  handleConfirmTime(e) {
    const val = e.detail.split(':')
    const {
      drugData,
      current
    } = this.data
    const index = drugData.reminds.findIndex((item) => `${Number(val[0])}:${Number(val[1])}` === `${item.hour}:${item.minite}`)
    if (index !== -1 && current !== index) {
      util.showToast({
        icon: 'none',
        title: '提醒时间不可设置同一时间'
      })
      this.setData({
        timeShow: false
      })
      return
    }
    this.setData({
      timeShow: false,
      [`drugData.reminds[${current}].hour`]: Number(val[0]),
      [`drugData.reminds[${current}].minite`]: Number(val[1])
    })
  },
  handleSetClockTime() {
    this.setData({
      timeShow: !this.data.timeShow
    })
  },
  handleShowSelect(e) {
    const {
      index,
      minite,
      hour
    } = e.currentTarget.dataset
    this.setData({
      timeShow: true,
      current: index,
      currentDate: `${hour}:${minite}`
    })
  },
  async handleSetClock(e) {
    const {
      type
    } = e.currentTarget.dataset
    util.showLoading({
      title: '保存中...',
      mask: true
    })
    try {
      const params = {
        operate: type,
        recomId: this.data.recomId,
        remindsList: this.data.drugData.reminds
      }
      const {
        data
      } = await util.request(api.drugremind, params, 'post')
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      } else {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000,
          success: () => {
            setTimeout(() => {
              wx.switchTab({
                url: '/pages/home/<USER>'
              })
            }, 2000)
          }
        })
      }
    } catch (error) {
      throw new Error(error)
    }
  },
  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {

  },

  /**
	 * 用户点击右上角分享
	 */
  onShareAppMessage: function() {

  }
})
