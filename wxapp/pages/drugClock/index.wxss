/**index.wxss**/
.container{
	padding-bottom: 150rpx;
}
.clock-item-right{
	height: 72rpx;
	border-radius: var(--containerRadius);
	background: #EEEEEE;
}
.clock-item-select{
	height: 56rpx;
}
.clock-item-image{
	width: 32rpx;
	height: 32rpx;
	right: 12rpx;
	top: 12rpx;
	position: absolute;
}
.fixed-btn-close{
	width: 180rpx;
	height: 80rpx;
	border: 2rpx solid #EEEEEE;
	border-radius: var(--btnRadius);
}
.fixed-btn-save{
	flex: 1;
	height: 80rpx;
	background: var(--btnThemeColor);
	border-radius: var(--btnRadius);
}