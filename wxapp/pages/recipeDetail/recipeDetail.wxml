<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view class="container" wx:if='{{detail.title}}' bindtouchmove="touchMove">
 
  <view class="head rel bg-color-white mt10">
    <view class="tc f36 b pt30">{{detail.title}}</view>
    <image  src="{{imgObject.ic_prescription_seal}}"></image>
  </view>
  <view class="info pt20 f24 pb30">
    <view class="flex_lr_m">
      <view class="flex1">编号：{{detail.serialNumber}}</view>
      <view class="flex1">时间：{{detail.recomTime}}</view>
    </view>
    <view class="flex_lr_m pt10">
      <view class="flex1">姓名：{{detail.patientName}}</view>
      <view class="flex1">性别：{{detail.patientGender ? '男' : '女'}}</view>
    </view>
    <view class="flex_lr_m pt10">
      <view class="flex1">年龄：{{detail.patientAgeStr}}</view>
      <view class="flex1">科室：{{detail.departmentName}}</view>
    </view>
    <view class="pt10">
      临床诊断：
        <text wx:for="{{detail.diagnosisList}}" wx:for-item="diagnosis" ><block wx:if="{{index!=0}}">、</block>{{diagnosis}}</text>
    </view>
  </view>
  <view class="rel pt30 pb30 info mt10">
    <view class="b f42 pb10">Rp：</view>
    <!-- 下方item除最后一条外需要增加bb1样式 -->
    <view wx:for="{{detail.items}}" wx:key="index" wx:for-item="medicationItem" class="item pt20 pb20 ">
      <view class="f30 c333 flex_lr p_item"><view>{{medicationItem.name}}</view><text class="p_num pl10">x{{medicationItem.quantity}}{{medicationItem.quantityUnit}}</text></view>
      <view class="c666 f24 pt10">用法用量：{{medicationItem.usages}}<block wx:if="{{medicationItem.backup}}">；备注：{{medicationItem.backup}}</block></view>
    </view>
    <view class="tr pt40 f32 c666">
      总金额：<text class="color-danger b">¥{{detail.totalPrice}}</text>
    </view>
    <view class="tag f32 b" wx:if="{{detail.status==1 || detail.status==2 || detail.status==4  }}">已购买</view>
    <view class="tag f32 b" wx:elif="{{detail.expire==1  }}">已失效</view>
  </view>
  <view class="info pl30 pr30 mt10 bb1">
    <view class=" pt30 pb30">
      <view class="lh40 c38BF87 f28">
        <!-- 0无需审核 1待审核 2已审核 -->
        <view wx:if="{{detail.checkingStatus == 1}}"><image src="{{imgObject.reviewedNot}}" class="icon"></image><text>待审核</text></view>
        <view wx:elif="{{detail.checkingStatus == 0 || detail.checkingStatus == 2}}"><image src="{{imgObject.reviewedOk}}" class="icon"></image><text>已审核</text></view>
        <view wx:else><image src="{{imgObject.reviewedNo}}" class="icon"></image><text>审核不通过</text></view>
      </view>
      <view class="pt20 lh34 c666 f28">
        医生：{{detail.doctorName}}
        <block wx:if="{{detail.handWriting && detail.drSealImage}}" >
          <image src="{{detail.drSealImage}}" class="sign" mode="heightFix"></image>{{detail.doctorAuditTime}}
        </block>
      </view>
      <view wx:if="{{detail.pharmacistName}}" class="pt10 lh34 c666 f28">
        审核药师：{{detail.pharmacistName}}
        <block wx:if="{{detail.handWriting && detail.phSealImage}}">
          <image src="{{detail.phSealImage}}" class="sign" mode="heightFix"></image>{{detail.pharmacistAuditTime}}
        </block>
      </view>
      <view class="pt10 lh34 c666 f28" wx:if='{{detail.alPharmacistName}}'>
        调配/发药：{{detail.alPharmacistName}}
        <block wx:if="{{detail.alPhSealImage && detail.alPharmacistAuditTime}}">
          <image src="{{detail.alPhSealImage}}" class="sign" mode="heightFix"></image>{{detail.alPharmacistAuditTime}}
        </block>
      </view>
    </view>
    <view>
      <view wx:if="{{detail.handWriting == 0 && detail.showDoctorSignature}}" class="pt10 lh34 c666 f28">
        医生：{{detail.doctorName}}<text>{{detail.signatureContent}}</text>{{detail.doctorAuditTime}}
      </view>
      <view wx:if="{{detail.handWriting == 0 && detail.showPharmacistSignature}}" class="pt10 lh34 c666 f28">
        药师：{{detail.pharmacistName}}<text>{{detail.signatureContent}}</text>{{detail.pharmacistAuditTime}}
      </view>
    </view>
  </view>
  <view class="p30 f22 c999 bg-color-white">
    注：处方48小时内有效且只能购买一次，如需继续用药，需要重新提交申请，由医生确认后方可购买
  </view>
</view>
<!-- 原公众号逻辑判断 -->
<view class="fixed b0 l0 w100 bg-color-white" wx:if="{{detail.checkingStatus == 0 || detail.checkingStatus == 2}}">
		<view class="confir pl30 pr30">
				<button bind:tap="download">查看处方pdf</button>
				<button class="{{type == 1 || !(detail.buy == 1||detail.expire == 1||detail.invalid == 1) ? '' : 'buyAgain'}} flex1 ml20" data-id="{{detail.recommendId}}" bindtap="buyDrug">
						<block wx:if="{{!(type == 1 || !(detail.buy == 1||detail.expire == 1||detail.invalid == 1))}}">
								申请再次购买
						</block>
						<block wx:else>{{type == 1 ? '立即复诊' : '购买药品'}}</block>
				</button>
		</view>
</view>
<view class="fixed b0 l0 w100 bg-color-white">
		<view wx:if="{{(detail.expire==1 && detail.buy == 0 && detail.require == 0) || (detail.buy == 1 && detail.require == 0)}}" class="confir pl30 pr30">
				<button bind:tap="download">查看处方pdf</button>
				<button class="flex1 ml20" data-id="{{detail.recommendId}}" bindtap="applyFor">申请再次购买</button>
		</view>
</view>
<authorization id='authToast' bind:authSub='onAuthSub' bind:close='onClose'></authorization>