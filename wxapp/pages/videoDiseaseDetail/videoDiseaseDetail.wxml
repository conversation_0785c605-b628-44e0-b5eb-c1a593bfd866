<view class="container">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="f24 p20 color-primary bg-color-blue-light" hover-class="none" hover-stop-propagation="false">
		复诊预约挂号后，需等待医生接诊，接诊通知会以服务
		号订阅消息推送，请收到通知后及时进入诊室。
	</view>
	<view class="w100 bg-color-white p30">
		<view class="f32 b c333">就诊人信息<text class="rd">*</text></view>
		<navigator open-type="navigate" hover-class="none" url="/pages/peopleContent/people/people?source=1">
			<view class="pt40">
				<view class="rel">
					<view class="c999 f32 lh44" wx:if="{{info.inquirerId==''}}">选择就诊人</view>
					<block wx:else>
						<view class="c333 b f36">{{peopleInfo.name}} <text
								class="pl30 pr30 n c666 f32">{{peopleInfo.gender===1?'男':'女'}} <text
									class="pl20">{{peopleInfo.age}}</text></text><text
								class="tag n  f24 ">{{peopleInfo.relationName}}</text></view>
						<view class="f28 c333 pt10">
							{{!peopleInfo.phone && !peopleInfo.guardianPhone ? '-':(peopleInfo.phone?peopleInfo.phone:peopleInfo.guardianPhone) }}
						</view>
					</block>
					<image src="{{imgObject.ic_more_black}}" class="noIcon"></image>
				</view>
			</view>
		</navigator>
	</view>
	<view class="illness w100 bg-color-white p30 pb15">
		<view class="f32 b c333 pb30">病情描述<text class="rd">*</text></view>
		<view class="illness-content bg-color-gray-light mgt30">
			<textarea placeholder="请输入您的病情描述..." value="{{info.illnessDescription}}" maxlength="1000"
				bindinput="textChange" placeholder-class="c999 f32"></textarea>
			<view class="maxlength p20 c999 f32" hover-class="none" hover-stop-propagation="false">
				{{info.illnessDescription.length}}/1000
			</view>
		</view>
	</view>

</view>
<view class="fixed b0 l0 w100" hover-class="none" hover-stop-propagation="false">
	<view class="b0 l0 w100 bg-color-white pl30 pr30 bt1 pb20">
		<view class="p20 flex_c_m" hover-class="none" hover-stop-propagation="false">
			<van-checkbox value="{{ checked }}" bind:change="onChange" data-type="checked" icon-size="30rpx"
				custom-class="dib">
			</van-checkbox>
			<navigator open-type="navigate" url="/pages/agreement/index?type=6" class="color-primary f24">
				《互联网诊疗风险告知知情同意书》
        </navigator>
		</view>
		<button class="fixed-btn" bindtap="pay"
			disabled="{{!info.inquirerId || !info.illnessDescription  || !checked}}">我已知晓，提交预约</button>
	</view>
</view>