// pages/diseaseDetail/diseaseDetail.js
var api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '就诊人病情',
    recordId: null, //病情ID；
    imgObject: {
      ic_upload_add: api.ImgUrl + 'images/ic_upload_add.png',
      ic_upload_delect: api.ImgUrl + 'images/ic_upload_delect.png',
      ic_more_black: api.ImgUrl + 'images/ic_more_black.png'
    },
    info: {
      // offlineSeeDoctor: 1, //1是  0否
      // offlineDiagnosis: '', //线下诊断
      // dataLossTag: 0, // 资料丢失0否 1是
      // offlineDiagnosisImgs: [], //图片
      inquirerId: '',
      illnessDescription: '' //病情描述
    },
    peopleInfo: {}, //所有就诊人ID
    templateId: [],
    clickFlag: true,
    checked: false,
    autoSize: {
      maxHeight: 100,
      minHeight: 100
    }

  },
  async getTemplate() {
    var templateId = await util.getTemplate(2)
    this.setData({
      templateId: templateId
    })
  },
  textChange(e) {
    this.setData({
      ['info.illnessDescription']: e.detail.value ? util.filterEmoji(e.detail.value) : e.detail.value
    })
    app.globalData.illnessDescription = e.detail.value
  },
  onChange(event) {
    this.setData({
      checked: event.detail
    })
  },
  // 添加就诊人病情
  async diseaseAdd() {
    var that = this
    util.showLoading({
      title: '提交中~',
      mask: true
    })
    try {
      const { data } = await 	util.request(api.diseaseAdd, that.data.info, 'post', 1)
      if (data.code === 0) {
        that.setData({
          recordId: data.data
        })
        return true
      } else {
        util.showToast({ title: data.msg })
        return false
      }
    } catch (error) {
      util.showToast({
        'title': '网络错误'
      })
      return false
    }
  },
  async disclaimer() {
    var that = this
    var params = {
      doctorId: that.data.doctorId,
      inquirerId: that.data.info.inquirerId,
      type: 2,
      agree: true,
      // recordId: that.data.recordId,
      consultType: that.data.type //图文 or 视频
    }
    try {
      const { data } = await util.request(api.disclaimer, params, 'post', 2)
      util.hideLoading()
      if (data.code === 0) {
        return data.data
      } else if (data.code === 210) {
        util.showModal({
          content: data.msg,
          showCancel: false,
          confirmText: '确定',
          success: function(res) {}
        })
      } else {
        util.showToast({
          title: data.msg
        })
      }
    } catch (error) {
      util.showToast({
        'title': '网络错误'
      })
    }
  },
  // 判断是否需要支付
  async pay() {
    var flag = await this.disclaimer()
    if (!flag && flag !== 0) {
      return
    }
    // !=0 去支付  =0免费
    if (flag != 0) {
      this.requestSubscribeMessage(1)
    } else {
      if (this.data.type == 1) {
        this.requestSubscribeMessage(2)
      } else {
        // 执行视频提交
        this.payinfo()
      }
    }
  },
  requestSubscribeMessage(type) {
    var that = this
    // 推送模板
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success(res) {
        that.goPage(type)
      },
      fail(res) {
        that.goPage(type)
      }
    })
  },
  goPage(type) {
    const that = this
    if (type == 1) {
      // 跳转支付页面
      wx.navigateTo({
        url: '/pages/pay/pay?doctorId=' + that.data.doctorId + '&type=' + that.data.type + '&recordId=' + that.data.recordId + '&inquirerId=' + that.data.info.inquirerId
      })
    } else if (type == 2) {
      wx.navigateTo({
        url: '/pages/consult/chat/chat?doctorId=' + that.data.doctorId + '&pageNum=3'
      })
    } else {
      // wx.navigateTo({
      //   url: '/pages/consult/record/index?doctorId=' + that.data.doctorId + '&type=2'
      // })
      app.globalData.consultType = 2
      wx.reLaunch({
        url: '/pages/consult/index/index?doctorName'
      })

    }
  },
  payinfo() {
    util.showLoading({
      title: '请稍后',
      mask: true
    })
    var that = this
    var data = {
      doctorId: that.data.doctorId,
      price: 0,
      conditionDesc: that.data.info.illnessDescription,
      // recordId: that.data.recordId,
      inquirerId: that.data.info.inquirerId
    }
    util.request(api.videoPayInfo, data, 'post', 2)
      .then(res => {
        util.hideLoading()
        if (res.data.code == 0) {
          this.requestSubscribeMessage(3)
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
  },
  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    this.setData({
      type: options.type,
      doctorId: options.doctorId,
      ['info.inquirerId']: options.inquirerId
    })
    this.getTemplate()
  },

  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow: async function() {
    var id = this.data.info.inquirerId
    var list = await util.getPeopleList()
    this.setData({
      peopleInfo: {},
      ['info.inquirerId']: ''
    })
    if (id) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].inquirerId === id) {
          list[i].phone = util.stringHidden(list[i].phone, 3, 4)
          list[i].guardianPhone = util.stringHidden(list[i].guardianPhone, 3, 4)
          this.setData({
            peopleInfo: list[i],
            ['info.inquirerId']: id
          })
          break
        }
      }
    } else {
      for (let i = 0; i < list.length; i++) {
        if (list[i].idCard && list[i].relation === 0) {
          list[i].phone = util.stringHidden(list[i].phone, 3, 4)
          this.setData({
            peopleInfo: list[i],
            ['info.inquirerId']: list[i].inquirerId
          })
          break
        }
      }
    }
  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {

  }

  /**
	 * 用户点击右上角分享
	 */
  // onShareAppMessage: function() {

  // }
})
