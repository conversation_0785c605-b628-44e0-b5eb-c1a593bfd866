/* pages/diseaseDetail/diseaseDetail.wxss */
.container{
  padding-bottom: 224rpx;
}
page{
  background: #fff;
}
textarea{
  width: 100%;
  height: 300rpx;
  border: 2rpx solid #EEEEEE;
  box-sizing: border-box;
  padding: 20rpx;border-radius: 8rpx;
  color: #333;
  font-size: 32rpx;
	border: none;
}
.drug text{
  height: 56rpx;
  box-sizing: border-box;
  border: 2rpx solid  #EEEEEE;
  border-radius: 4rpx;
  display: inline-block;
  font-size: 28rpx;
  color: #999999;
  padding: 0 16rpx;
  line-height: 52rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.drug text.cur{
  background: var(--themeColor);
  border-color: var(--themeColor);
  color: #fff;
}
.upload{
  float:left;
  width:140rpx;
  height:140rpx;
  margin-top: 20rpx;
}
.upload image{
  display: block;
  width:100%;
  height:100%;
}
.picList .item{
  display: block;
  float:left;
  margin-right:40rpx;
  margin-top: 20rpx;
  width: 140rpx;
  height: 140rpx;
  position: relative;
  overflow: visible;
}
.picList .item .delPic{
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: -20rpx;
  top:-20rpx;
}
.picList .item .delPic image{
  display: block;
  width: 100%;
  height: 100%;
}
.picList .item>image{
  display: block;
  width:100%;
  height:100%;
}
.picList .item:nth-child(4n){
  margin-right: 0rpx;
}
.confir button.gray{
  background: #ddd;
}
.noIcon{
  width: 44rpx;
  height: 44rpx;
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -22rpx;
}
.lh44{
  line-height: 44rpx;
}
.tag{
  background: #E7F4FF;
  border-radius: 4rpx;
  width: 76rpx;
  height:36rpx ;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  color: var(--themeColor);
}
/* 新增样式 */
.illness-content {
	width: 100%;
  height: 300rpx;
  border-radius: 20rpx;
  /* border: 1px solid #eeeeee; */
  display: flex;
  align-items: center;
  background: #F8F8F8;
	display: flex;
	flex-direction: column;
}
.maxlength{
	width: 100%;
	text-align: right
}
.fixed-btn{
  height: 96rpx;
  line-height: 96rpx;
  font-size: 32rpx;
  color: #fff;
  border-radius: var(--btnRadius);
  background-color: var(--btnThemeColor);
}
.rd {
  margin-left: 8rpx;
  color: #ff5555;
}
.illness{
  border-top: 20rpx solid #f8f8f8;
}