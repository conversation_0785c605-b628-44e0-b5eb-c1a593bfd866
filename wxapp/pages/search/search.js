// pages/people/people.js
var api = require('../../config/api.js')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    histroy: [],
    keyWord: '',
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '搜索',
    static: {
      ic_search_deleted: api.ImgUrl + 'images/ic_search_deleted.png'
    }
  },
  changeKeyWord(e) {
    this.setData({
      keyWord: util.filterEmoji(e.detail.value)
    })
  },
  changeSearch(e) {
    const keyWord = this.data.keyWord
    if (keyWord == '') {
      util.showToast({ title: '搜索内容不能为空~' })
    } else {
      var oldkeyWord = wx.getStorageSync('keyWord')
      var newkeyWord
      if (oldkeyWord == '') {
        newkeyWord = keyWord.split(',')
      } else {
        !oldkeyWord.includes(keyWord) ? oldkeyWord.unshift(keyWord) : oldkeyWord
        newkeyWord = oldkeyWord
      }
      wx.setStorageSync('keyWord', newkeyWord)
      wx.navigateTo({
        url: '/pages/famousDoctor/famousDoctor?keyword=' + keyWord
      })
    }
  },
  godoctorList(e) {
    var keyWord = e.currentTarget.dataset.value
    wx.navigateTo({
      url: '/pages/famousDoctor/famousDoctor?keyword=' + keyWord
    })
  },
  deleteKeyWord() {
    wx.navigateBack({
      delta: 1
    })
  },
  deleteHistory() {
    var that = this
    util.showModal({
      content: '确认清除历史记录？',
      showCancel: true,
      cancelText: '取消',
      cancelColor: '#666666',
      confirmText: '确认',
      success: function(res) {
        if (!res.cancel) {
          wx.removeStorageSync('keyWord')
          that.setData({
            histroy: []
          })
        }
      }
    })

  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.setData({
      histroy: wx.getStorageSync('keyWord')
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
})
