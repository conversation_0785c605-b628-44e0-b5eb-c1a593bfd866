/* pages/search/search.wxss */
.search{
  width: 610rpx;
  height: 80rpx;
  background: #F8F8F8;
  border-radius: var(--btnRadius);
  line-height: 80rpx;
}
.search input{
  display: block;
  width: 100%;
  height: 100%;
  padding-left: 88rpx;
  box-sizing: border-box;
  padding-right: 80rpx;
}
.search .icon{
  width: 40rpx;
  height: 40rpx;
  left: 32rpx;
  top: 20rpx;
}
.remove{
  width: 108rpx;
  text-align: center;
  height: 80rpx;line-height: 80rpx;
}
.title image{
  display: block;
  float: right;
  width: 40rpx;
  height: 40rpx;
}
.content text{
  margin-top: 28rpx;
  margin-right: 20rpx;
  padding: 0 16rpx;
  height: 56rpx;
  line-height: 56rpx;
  background: #F5F5F5;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #999999;
  display: inline-block;
  white-space:nowrap;
  max-width: 690rpx;

  /* display: -webkit-box;
  -webkit-line-clamp: 1; */
  word-wrap: break-word;
  word-break: break-all;
  overflow: hidden;
  text-overflow: ellipsis;
  /* -webkit-box-orient: vertical; */
}
.content {
  padding-bottom: 20rpx;
}