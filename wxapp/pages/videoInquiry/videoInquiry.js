// pages/auth/videoInquiry/videoInquiry.js
var api = require('../../config/api')
var util = require('../../utils/util')
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    ImgUrl: api.ImgUrl,
    dataList: [],
    listQuery: {
      page: 1 // 页码
    },
    static: {
      nomes: api.ImgUrl + 'images/nomes.png'
    }
  },

  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    this.getRecordList()
  },
  getRecordList() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.recordList, {}, 'GET')
      .then(res => {
        util.hideToast()
        const result = res.data.data
        if (res.data.code === 0) {
          ++this.data.listQuery.page
          this.setData({
            dataList: this.data.dataList.concat(result.result),
            listQuery: this.data.listQuery
          })
          if (!result.hasNext) {
            this.setData({
              loadComplete: false
            })
          }
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(err => {
      })
  },
  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow: function() {

  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {
    if (this.data.loadComplete) {
      this.getRecordList()
    }
  }

  /**
	 * 用户点击右上角分享
	 */
  // onShareAppMessage: function() {

  // }
})
