/* pages/orderDetail/orderDetail.wxss */
page{
  background: #F8F8F8;
}
.head{
  width: 100%;
  position: relative;
  height: 168rpx;
}
.head .content{
   position: absolute;
   left: 0;
   top: 0;
   width: 100%;
   height: 100%;
   line-height: 168rpx;
   font-size: 32rpx;
   color: #fff;
   font-weight: bold;
}
.head .content image{
  display: inline-block;
  width: 44rpx;
  height: 44rpx;
  vertical-align: top;
  margin-top: 62rpx;
  margin-right: 10rpx;
}
.head .content text{
  font-weight: normal;
  font-size: 26rpx;
  padding-left: 40rpx;
}
.consignee{
  background: #fff;
}
.consignee .MapIcon{
  width: 44rpx;
  height: 44rpx;
}
.title{
  height: 92rpx;
  line-height: 92rpx;
}
.img image{
  width: 100rpx;
  height: 100rpx;
  border: 1rpx solid #eee;
	border-radius: 8rpx;
}
.confir .fixed_btn{
  width: 170rpx;
  height: 56rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
}
.confir .fixed_btn:nth-child(1){
  color: #666;
  border:2rpx solid #E4E4E4;
  background: #fff;
}
.confir .fixed_btn:nth-child(2){
  color: #fff;
  background: #367DFF;
	margin-left: 30rpx;
}
.pbbtn{
	padding-bottom: 150rpx;
}