<!--pages/famousDoctor/famousDoctor.wxml-->
<view class="container bg-color-gray-light">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="fixed zx2" style="width:100%;">
		<view class="searchBox pl30 pt10 pb10 clearfix pr30 bg-color-white">
			<view class="search rel fl ">
				<view class="icon abs">
					<image src="{{imgObject.search}}" mode="aspectFill" class="imgBlock"></image>
				</view>
				<input type="text" class="f28 c333" placeholder-class="f28 c999" placeholder="医生姓名、医院名称"
					bindconfirm='changeSearch' value="{{keyWord}}" bindinput='changeKeyWord' maxlength="35" />
				<view class="delet" hidden="{{!keyWord}}" bindtap="deleteKeyWord">
					<image src="{{imgObject.del}}"></image>
				</view>
			</view>
		</view>
		<view class="w100 Select">
			<view class="SelectHead">
				<view bindtap="SelectFun" data-id="1">
					<text class="{{departmentFlag?'cur':''}}">{{departmentName}}</text>
					<image src="{{imgObject.dropdown}}" wx:if="{{!departmentFlag}}"></image>
					<image src="{{imgObject.dropUp}}" wx:elif="{{departmentFlag && departmentslider}}"></image>
					<image src="{{imgObject.down_blue}}" wx:else></image>
				</view>
				<view bindtap="SelectFun" data-id="3">
					<text class="{{diseaseFlag?'cur':''}}">{{diseaseDefaultName}}</text>
					<image src="{{imgObject.dropdown}}" wx:if="{{!diseaseFlag}}"></image>
					<image src="{{imgObject.dropUp}}" wx:elif="{{diseaseFlag && diseaseSlider}}"></image>
					<image src="{{imgObject.down_blue}}" wx:else></image>
				</view>
				<view bindtap="SelectFun" data-id="2">
					<text class="{{citysFlag?'cur':''}}">{{citysName}}</text>
					<image src="{{imgObject.dropdown}}" wx:if="{{!citysFlag}}"></image>
					<image src="{{imgObject.dropUp}}" wx:elif="{{citysFlag && cityslider}}"></image>
					<image src="{{imgObject.down_blue}}" wx:else></image>
				</view>
			</view>
			<!-- 选择科室 -->
			<view class='SelectBox' catch:touchmove="stopMove" hidden="{{!departmentslider}}">
				<scroll-view class="sliderItem fl" scroll-y="true">
					<view wx:for="{{departments}}" wx:key="index"
						class="w100 pt20 pb20 c666 f28 tc {{index==departmentChoose?'cur':''}}" bindtap="ChooseFun" data-type='0'
						data-index="{{index}}" data-id="{{item.id}}" data-model="0">{{item.name}}</view>
				</scroll-view>
				<scroll-view class="sliderItem fl bg-color-gray-light" scroll-y="true" scroll-into-view="{{departToView}}">
					<view wx:for="{{departments[departmentChoose].childs}}" id="{{index==0?'departElement':''}}" wx:key="index"
						class="w100 pt20 pb20 c666 f28 tc  {{item.id==departmentId?'cur':''}}" bindtap="ChooseFun" data-type='1'
						data-id="{{item.id}}" data-index="{{index}}" data-value="{{item.name}}" data-model="0">{{item.name}}</view>
				</scroll-view>
			</view>
			<!-- 选择地区 -->
			<view class='SelectBox' catch:touchmove="stopMove" hidden="{{!cityslider}}">
				<scroll-view class="sliderItem fl" scroll-y="true">
					<view wx:for="{{citys}}" wx:key="index" class="w100 pt20 pb20 c666 f28 tc {{index==citysChoose?'cur':''}}"
						bindtap="ChooseFun" data-type='0' data-index="{{index}}" data-id="{{item.id}}" data-model="1">{{item.name}}
					</view>
				</scroll-view>
				<scroll-view class="sliderItem fl bg-color-gray-light" scroll-y="true" scroll-into-view="{{cityToView}}">
					<view wx:for="{{citys[citysChoose].childs}}" id="{{index==0?'cityElement':''}}" wx:key="index"
						class="w100 pt20 pb20 c666 f28 tc  {{item.id==citysId?'cur':''}}" bindtap="ChooseFun" data-type='1'
						data-id="{{item.id}}" data-index="{{index}}" data-value="{{item.name}}" data-model="1">{{item.name}}</view>
				</scroll-view>
			</view>
			<!-- 选择常见疾病 -->
			<view class='SelectBox' catch:touchmove="stopMove" hidden="{{!diseaseSlider}}">
				<scroll-view class="sliderItem sliderDiseaseItem fl" scroll-y="true">
					<view wx:for="{{commonDisease}}" wx:key="index" class="w100 pt20 pb20 c666 f28 tc"
						bindtap="ChooseFun" data-type='0' data-index="{{index}}" data-id="{{item.id}}" data-value="{{item.name}}" data-model="2">{{item.name}}
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
	<view style="padding-top:184rpx">
		<doctorList list='{{list}}' type='{{3}}'></doctorList>
	</view>
	<view class="{{(departmentslider || cityslider || diseaseSlider) ?'propMain':''}}" catch:touchmove="stopMove"
		wx:if="{{departmentslider || cityslider || diseaseSlider}}" bindtap="closeChoose"></view>
</view>