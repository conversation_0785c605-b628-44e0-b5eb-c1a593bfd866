/* pages/recipe/recipe.wxss */
page{
  background: #F8F8F8;
}
.tabBars{
  height: 84rpx;
  background: #fff;
}
.TabsItem view{
  font-size: 28rpx;
  color: #666666;
}
.activeTab view{
  color: var(--themeColor);
  font-weight: bold;
} 
.tabBars  .van-tabs__line{
  border-radius: 2rpx !important;
}
.h2{
  padding-top: 16rpx;
  overflow: hidden;
  text-overflow:ellipsis; white-space: nowrap;
}
.btn{
  width: 172rpx;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid var(--themeColor);
  text-align: center;
  line-height: 56rpx;
  float: right;
  font-size: 28rpx;
  color: var(--themeColor);
  border-radius: 30rpx;
}
.cF06454{color: var(--redColor);}
.c38BF87{color:var(--labelColor);}
.tab{
  width: 100%;
  height: 84rpx;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}
.tab view{
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  height: 84rpx;
  line-height: 84rpx;
  /* width: 25%; */
}
.tab view text{
  display: inline-block;
  height: 100%;
  position: relative;
}
.tab view text.cur{
  color: var(--themeColor);
  font-weight: bold;
}
.tab view text.cur::after{
  position: absolute;
  content: '';
  width: 40rpx;
  height: 8rpx;
  border-radius: 2rpx;
  background: var(--themeColor);
  left: 50%;
  margin-left: -20rpx;
  bottom: 0rpx;
}
.item-box{
	border-radius: 20rpx;
}