// pages/recipe/recipe.js
const api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
	 * 页面的初始数据
	 */

  data: {
    isBack: true,
    backgroundColor: '#fff',
    activeColor: util.THEMECOLOR,
    navTitle: '我的处方',
    type: null, //判断是复诊 还是处方 复诊不显示待购买的状态
    listQuery: {
      status: '-1',
      page: 1,
      num: 10
    },
    presList: [],
    statusBarHeight: null,
    status: [{
      id: '-1',
      name: '全部'
    },
    {
      id: '0',
      name: '待购买'
    },
    {
      id: '1',
      name: '已购买'
    }
    ],
    static: {
      nomes: api.ImgUrl + 'images/nomes.png'
    }
  },
  tabClick(e) {
    this.setData({
      'listQuery.page': 1,
      'listQuery.status': e.currentTarget.dataset.id
    })
    if (wx.pageScrollTo) {
      wx.pageScrollTo({
        scrollTop: 0
      })
    }
    this.getList()
  },
  // 立即申请
  applyFor(e) {
    var name = e.currentTarget.dataset.name
    this.reapply(e.target.dataset.id, name)
  },
  async reapply(recommendId, name) {
    try {
      util.showLoading({
        title: '提交中',
        mask: true
      })
      const {
        data
      } = await util.request(api.prescriptionReapply, {
        recommendId
      }, 'post', 2)
      util.hideLoading()
      var that = this
      // TODO: 申请后 暂定刷新列表
      if (data.code === 0) {
        util.showModal({
          content: '您的求药申请已发送给' + name + '医生，医生会第一时间为您续方，请耐心等待~',
          showCancel: false,
          confirmText: '我知道了',
          success: (result) => {
            if (result.confirm) {
              that.setData({
                'listQuery.page': 1
              })
              that.getList()
            }
          }
        })
      } else if (data.code === 210) {
        util.showModal({
          content: data.msg,
          showCancel: false,
          confirmText: '确定',
          success: function(res) {}
        })
      } else {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },

  async getList() {
    // 复诊请求需要增加一个type参数
    util.showLoading({
      title: 'loading',
      mask: true
    })
    try {
      const {
        listQuery,
        presList,
        type
      } = this.data
      const {
        data
      } = await util.request(api.prescriptionList, {
        ...listQuery,
        status: listQuery.status === '-1' ? null : listQuery.status,
        type: type === '1' ? type : null
      })
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      data.data.result = data.data.result.map(item => {
        item.drugNames = item.items.map(drug => drug.name).join('、')
        return item
      })

      this.setData({
        presList: listQuery.page > 1 ? presList.concat(data.data.result) : data.data.result,
        'listQuery.page': listQuery.page + 1,
        loadComplete: !data.data.hasNext ? false : true
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  goDetail(e) {
    const {
      drugtype,
      recomid
    } = e.currentTarget.dataset
    const url = drugtype =='1' ? `/pages/recipeDetail/recipeDetail?recomId=${recomid}` : `/pages/recipeTcmDetail/index?recomId=${recomid}`
    wx.navigateTo({
      url
    })
  },
  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    wx.setNavigationBarTitle({
      title: options.type === '1' ? '续方申请' : '我的处方'
    })
    this.setData({
      type: options.type || '2',
      navTitle: options.type === '1' ? '续方申请' : '我的处方',
      statusBarHeight: app.globalData.statusBarHeight
    })
  },

  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady: function() {

  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow: function() {
    this.setData({
      ['listQuery.page']: 1
    })
    this.getList()
  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {},

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {

    if (this.data.loadComplete) {
      // this.setData({
      //   ['listQuery.page']: ++this.data.listQuery.page
      // })
      this.getList()
    }

  }

  /**
	 * 用户点击右上角分享
	 */
  // onShareAppMessage: function() {

  // }
})
