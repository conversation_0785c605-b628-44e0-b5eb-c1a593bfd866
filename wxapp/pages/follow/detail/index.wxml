<navbar isBack="{{true}}" backgroundColor='#fff' navTitle="随访详情"></navbar>
<view class="container bg-color-gray-light flex_line"
	style="{{content.type == 1 && content.status == 1 ? 'padding-bottom: 160rpx;':''}}">
	<view class="p30 bg-color-white mb20">
		<view class="f28 c999">
			随访人信息
		</view>
		<view class="f32 c333 mt20">
			{{content.inquirerName}}｜{{content.inquirerGender}}｜{{content.inquirerAge}}
		</view>
	</view>
	<view class="p30 bg-color-white mb20">
		<view class="f28 c999">
			随访医生
		</view>
		<view class="f32 c333 mt20">
			{{content.doctorName}}
		</view>
	</view>
	<view class="p30 bg-color-white mb20">
		<view class="f28 c999">
			随访类型
		</view>
		<view class="f32 c333 mt20">
			{{content.type == 1 ? '随访复诊':'随访问卷'}}
		</view>
	</view>
	<view class="p30 bg-color-white mb20">
		<view class="f28 c999">
			随访名称
		</view>
		<view class="f32 c333 mt20">
			{{content.name}}
		</view>
	</view>
	<view class="p30 bg-color-white mb20">
		<view class="f28 c999">
			{{content.type == 1 ? '复诊时间':'执行时间'}}
		</view>
		<view class="f32 c333 mt20">
			{{content.beginTime}}
		</view>
	</view>
	<view class="p30 bg-color-white mb20 flex1" wx:if='{{content.type == 1 && content.visitRecordType != 0}}'>
		<view class="f28 c999">
			复诊记录
		</view>
		<view class="flex_lr_m bg-color-gray-light p20 mt20 color-primary" bind:tap="handleGohistory" wx:if="{{content.visitRecordType == 1}}">
			<view class="f28 container-radius" >
				查看「线上」复诊记录
			</view>
			<view class="flex_m f24 tr ml20 color-primary">
				<text class="rigth-icon f10"></text>
			</view>
		</view>
		<view class="f32 c333 mt20" wx:else>
			{{content.visitRecord}}
		</view>
	</view>

	<view class="p30 bg-color-white mb20" wx:if='{{content.type==2}}'>
		<view class="f28 c999">
			执行医嘱
		</view>
		<view class="f32 c333 mt20">
			{{content.medicalAdvice}}
		</view>
	</view>
	<view class="p30 bg-color-white mb20" wx:if='{{content.type==2}}'>
		<view class="f28 c999">
			执行问卷
		</view>
		<view class="f32 c333 mt20">
			<view class="flex_lr_m bg-color-gray-light p20 mb20" data-formId='{{i.followUpRecordFormId}}'
				data-id='{{content.id}}' bind:tap="handleGoFrom" wx:for="{{content.followUpFormList}}" wx:for-item="i"
				wx:for-index="i_index">
				<view class="c333 f28 flex1 flex_m container-radius">
					<image style="width:40rpx;height:40rpx" src="{{static.ic_form_black}}" mode="aspectFill" />
					<view>
						{{i.formName}}
					</view>
				</view>
				<view class="flex_m f24 tr ml20 {{i.fillingStatus == 0 ? 'c999':'color-success'}}">
					{{i.fillingStatus == 0 ? '未填写':'已填写'}}
					<text class="rigth-icon f10"></text>
				</view>
			</view>
		</view>
	</view>
	<view class="p30 bg-color-white mb20 flex1" wx:if="{{content.status === 3}}">
		<view class="f28 c999">
			取消原因
		</view>
		<view class="f32 c333 mt20">
			{{content.reason}}
		</view>
	</view>
	<view class="fixed-button flex_m bt1" wx:if='{{content.type == 1 && content.canConsult}}'>
		<navigator url='/pages/consult/chat/chat?doctorId={{content.doctorId}}' class="bttom-btn">去问诊</navigator>
	</view>
</view>