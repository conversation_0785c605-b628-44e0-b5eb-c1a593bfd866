.calehead {
  display: flex;
  align-items: center;
  justify-content: center;
}
.calendar .elegant_choosed {
  color: #fff;
  background-color: var(--themeColor);
}
.calehead {
  background-color: #2893ff;
  height: 94rpx;
  border-top-left-radius: 10rpx;
  border-top-right-radius: 10rpx;
  /* 垂直水平居中 */
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}
.content{
	margin-top: 322px;
}
.list-item-icon {
  width: 35rpx;
  height: 35rpx;
}
.list-item {
  border-right: 6rpx solid #f05542;
}

.no_msg {
  width: 420rpx;
  height: 312rpx;
  margin-top: 160rpx;
}
.list-item-tag_fail {
  width: 80rpx;
  height: 32rpx;
  border-radius: 4rpx;
  border: 1rpx solid #ccc;
}
.list-item-tag_primary {
  width: 80rpx;
  height: 32rpx;
  border-radius: 4rpx;
  border: 1rpx solid #2893ff;
}

.calendar-box {
  width: 100%;
  position: -webkit-sticky;
	position: sticky;
  left: 0;
  z-index: 9;
}
