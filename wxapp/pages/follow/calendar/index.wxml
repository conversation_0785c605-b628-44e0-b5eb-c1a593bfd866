<navbar backgroundColor='#fff' navTitle="随访计划"></navbar>
<view class="container bg-color-gray-light flex_line">
	<view class="calendar-box" style="top:{{44+statusBarHeight}}px">
		<calendar id="calendar" calendarConfig="{{ calendarConfig }}" bind:onChooseDate='onTapMonth'
			bind:afterTapDay="onTapDay">
		</calendar>
	</view>
	<view class="list" wx:if='{{list.length}}'>
		<view class="bg-color-white m20 p20 container-radius flex {{item.complete?'list-item':''}}" wx:for="{{list}}"
			data-id='{{item.id}}' bind:tap="goDetail">
			<view class="flex1 ml10">
				<view class="flex_lr">
					<view class="flex_m">
						<image class="list-item-icon" wx:if='{{item.type == 1}}' src="{{static.ic_follow_visit}}"
							lazy-load="false" />
						<image class="list-item-icon" wx:else src="{{static.ic_follow_questionnaire}}" />
						<text class="f32 b c333 ml10">{{item.type == 1 ? '随访复诊':'随访问卷'}}</text>
						<text wx:if='{{item.status==3}}' class="list-item-tag_fail flex_c_m f20 c999 ml30">已取消</text>
					</view>
					<text class="f24 color-danger" wx:if='{{item.type==2 && item.complete}}'>有未填写问卷</text>
				</view>
				<view class="ml45 f24 c666 mt10">
					<text>随访人：{{item.inquirerName}} {{item.inquirerGender}} {{item.inquirerAge}}</text>
				</view>
				<view class="ml45 f24 c666 mt10">
					<text>随访名称：{{item.name}}</text>
				</view>
			</view>
		</view>
	</view>
	<view class="flex_line_c bg-color-white flex1" wx:else>
		<image class="no_msg" src="{{static.img_blank_noprescription}}"></image>
		<view class="f28 c666">当前暂无随访计划</view>
	</view>
</view>