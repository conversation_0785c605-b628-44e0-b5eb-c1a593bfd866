.search-history {
		margin-top: 20rpx;
}
.search-history .title-wrap {
		display: flex;
		justify-content: space-between;
    font-size: 32rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    color: #333333;
		margin: 0 20rpx;
}
.search-history image {
		width: 44rpx;
		height: 44rpx;
}
.text-list {
		margin-left: 28rpx;
		margin-top: 20rpx;
		display: flex;
		flex-flow: wrap;
}
.text-list .text-item {
    margin-right: 20rpx;
		margin-bottom: 20rpx;
		padding: 8rpx;
    background: #F5F5F5;
    border-radius: 8rpx;
    font-size: 28rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #999999;
}
.item-wrap {
		position: relative;
		padding-bottom: 20rpx;
}
.item-wrap::after {
		content: '';
		display: block;
		width: 700rpx;
		height: 0.5rpx;
		background: #eee;
		position: absolute;
		left: 25rpx;
		bottom: 0;
}
.condition-bar {
		padding: 0 40rpx;
		border-bottom: 0.5rpx solid #eee;
		height: 76rpx;
		display: flex;
		align-items: center;
    font-size: 26rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333;
}
.condition-bar .active {
		 color: #367DFF;
}
.condition-bar .condition-item {
		margin-right: 60rpx;
		display: flex;
		align-items: center;
}
.condition-bar .condition-item .orderby-img {
		width: 36rpx;
		height: 36rpx;
}
