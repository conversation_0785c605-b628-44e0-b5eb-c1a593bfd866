/* pages/orderList/orderList.wxss */
page{
	background-color: var(--bgColor);
}
.tab{
  width: 100%;
  height: 84rpx;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}
.tab view{
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  height: 84rpx;
  line-height: 84rpx;
  /* width: 25%; */
}
.tab view text{
  display: inline-block;
  height: 100%;
  position: relative;
}
.tab view text.cur{
  color: var(--themeColor);
  font-weight: bold;
}
.tab view text.cur::after{
  position: absolute;
  content: '';
  width: 40rpx;
  height: 8rpx;
  border-radius: 2rpx;
  background: var(--themeColor);
  left: 50%;
  margin-left: -20rpx;
  bottom: 0rpx;
}
.orderList {
  width: 100%;
  background: #F8F8F8;
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}
.orderList .item{
  width: 100%;
  box-sizing: border-box;
  background: #fff;
  margin-bottom: 20rpx;
  padding: 0 32rpx;
}
.orderList .item:last-child{
  margin-bottom: 0rpx;
}
.orderList .item .orderHead{
  width: 100%;
  box-sizing: border-box;
  padding:32rpx 0;
  font-size: 28rpx;
  color: #666666;
}
.orderList .item .orderHead .status{
  float: right;
  font-size: 32rpx;
  font-weight: bold;
}
.orderContent{
  padding: 32rpx 0;
}
.orderContent .shopItem{
  overflow: hidden;
  margin-bottom: 20rpx;
}
.orderContent .shopItem:last-child{
  margin-bottom: 0rpx;
}
.orderContent .shopItem .img{
  float: left;
  width: 160rpx;
  height: 160rpx;
  line-height: 160rpx;
}
.orderContent .shopItem .img image{
  display: inline-block;
  text-align: center;
  width: 100%;
  height: 100%;
}
.orderContent .shopItem .info{
  float: right;
  width: 506rpx;
}
.orderContent .shopItem .info .title{
  font-weight: bold;
  color: #333;
  line-height: 44rpx;
}
.orderContent .shopItem .info .clearfix{
  margin-top: 56rpx;
  font-weight: bold;
}
.orderContent .shopItem .info .clearfix .price{
  float: left;
  font-size: 32rpx;
  color:var(--redColor);
}
.orderContent .shopItem .info .clearfix .count{
  float: right;
  font-size: 32rpx;
  color: #333;
}
.orderFot{
  padding:32rpx 0;
  box-sizing: border-box;
  width: 100%;
}
.orderFot .total{
  text-align:right;
  font-size: 28rpx;
}
.prompt{
  padding-top: 8rpx;
  padding-bottom: 20rpx;
  text-align: right;
  color: var(--redColor);
}
.btn{
  text-align: right;
}
.btn text{
  display: inline-block;
  height: 60rpx;
  width: 172rpx;
  text-align: center;
  font-size: 24rpx;
  margin-left: 32rpx;
  line-height: 60rpx;
  border-radius: 8rpx;
  line-height: 56rpx;
  box-sizing: border-box;
}
.removeBtn{
  border:2rpx solid #999 ;
  color: #666;
}
.payBtn{
  background: var(--themeColor);
  border: 2rpx solid var(--themeColor);
  color: #fff;
}