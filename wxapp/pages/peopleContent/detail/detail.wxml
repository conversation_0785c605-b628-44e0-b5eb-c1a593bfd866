<view class='container'>
  <navbar isBack='{{isBack}}' home='{{!isBack}}' backgroundColor='{{backgroundColor}}' navTitle='{{navTitle}}'></navbar>
  
  <!-- 基本信息 -->
  <view class='bg-color-white info m20'>
    <view class="section-title">基本信息</view>
    <van-cell-group>
      <van-field value='{{ info.name }}' label='姓名' disabled='{{isEdit}}' input-class="{{!isEdit?'c333':''}}" input-align='right' readonly='{{isEdit}}' placeholder='请输入就诊人姓名' bind:input='nameChange' data-type='name' />
      <van-field value="{{ info.idCard?info.idCard:''}}" type="idcard" label='身份证号' input-class="{{!isEdit?'c333':''}}" input-align='right' readonly='{{isEdit}}' disabled='{{isEdit}}' placeholder='请输入就诊人身份证{{model==0?"":"（选填）"}}' bind:change='idCardChange' data-type='idCard' maxlength="18" />
      <van-field value="{{ info.gender==1?'男':'女' }}" label='性别' input-align='right' readonly disabled='{{isEdit}}' wx:if='{{isEdit}}' />
      <van-field value='{{ info.birthday }}' label='出生日期' input-align='right' readonly disabled='{{isEdit}}' wx:if='{{isEdit}}' />
      <van-field value='{{ info.phone }}' input-class='c333' type='number' bind:blur='inputChange' data-type='phone' label='手机号码' placeholder='就诊人手机号' input-align='right' wx:if='{{model==0}}' maxlength="11">
        <button slot="button" open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" class="authbtn btn1 f28">
          微信授权
        </button>
      </van-field>
    </van-cell-group>
    <!--star 儿童就诊人未添加身份证号时候显示 -->
    <van-cell title='性别' wx:if="{{!isEdit && model==1}}" value='{{info.gender==1?"男":"女"}}' is-link bind:click='showAddress' data-type='1' />
    <van-cell title='出生日期' wx:if="{{!isEdit && model==1}}" value='{{info.birthday}}' is-link bind:click='showAddress' data-type='2' />
    <!-- end -->
    <van-cell title='就诊人是您的' value='{{info.relationName}}' is-link bind:click='showAddress' data-type='3' />
    <van-cell title="婚姻状况">
      <radio-group value='{{info.maritalStatus}}' bindchange="onMaritalChange">
        <radio value="0" checked="{{info.maritalStatus=='0'}}"><text class="f32 ml10">未婚</text></radio>
        <radio value="1" checked="{{info.maritalStatus=='1'}}" class="ml20"><text class="f32 ml10">已婚</text></radio>
      </radio-group>
    </van-cell>
    <!-- 紧急联系人 -->
    <view class='bg-color-white mt20 info'>
      <van-field label='紧急联系人姓名' value='{{ info.contactName }}' input-align='right' placeholder='请输入' data-key='contactName' bind:input='onInputVal' />
      <van-field label='紧急联系人手机号码' value='{{ info.contactPhone }}' type='number' maxlength="11" input-align='right' placeholder='请输入' data-key='contactPhone' bind:input='onInputVal' />
    </view>
  </view>
  
  <!-- 收货地址 -->
  <view class='bg-color-white mt20 info m20'>
    <view class="section-title">收货地址</view>
    <van-field label='收货人' value='{{ info.consignee }}' input-align='right' placeholder='请输入收货人姓名' data-key='consignee' bind:input='onInputVal' />
    <van-field label='手机号' value='{{ info.consigneePhone }}' type='number' maxlength="11" input-align='right' placeholder='请输入收货人手机号' data-key='consigneePhone' bind:input='onInputVal' />
    <AddresPicker label='所在地区' placeholder='请选择所在省市区' areaValue='{{info.addressPrefix}}' inputAlign='right' bind:onPickerConfim='onPickerConfim'>
    </AddresPicker>
    <van-field label='详细地址' value='{{ info.address }}' input-align='right' placeholder='街道、门牌号等详细地址' bind:input='onInputVal' data-key='address' />
  </view>
  
  <!-- 健康史 -->
  <view class='bg-color-white mt20 info m20'>
    <view class="section-title" style="padding-bottom: 0;">健康史</view>
    <medicalHistory allergy='{{allergy}}' isDetail='{{isDetail}}' always='{{always}}' bind:propContent='propContent'>
    </medicalHistory>
  </view>
  <view class="flex p30 agreement">
    <view style="w100 flex">
      <van-checkbox value="{{ checked }}" bind:change="onChange" data-type="checked" icon-size="28rpx" custom-class="dib"><text class="c666 f24">我已阅读并同意</text><text class="f24 color-primary" data-type='11' catchtap='goAgreement'>《用户协议》</text><text class="f24 color-primary" data-type='12' catchtap='goAgreement'>《隐私政策》</text>
      </van-checkbox>
    </view>
  </view>
  <view class='fixed b0 l0 w100 bg-color-white p30 flex_m'>
    <view class='del f28 c999 flex_c_m' bindtap='del'>删除</view>
    <view class='save f28 cfff flex_c_m ml30 bg-color-primary' bindtap='save'>保存</view>
  </view>
  <van-popup show='{{showPicker}}' bind:close='showAddress' close-on-click-overlay='{{true}}' round='{{true}}' position='bottom'>
    <view class='relationPicker' catchtap='return' wx:if="{{relationBoxShow}}">
      <van-picker columns='{{ relationList }}' show-toolbar title='与就诊人关系' bind:cancel='onCancel' bind:confirm='confirRelation' value-key='relationName' wx:if='{{model==0}}' />
      <van-picker columns='{{ relationListChild }}' show-toolbar title='与就诊人关系' bind:cancel='onCancel' bind:confirm='confirRelation' value-key='relationName' wx:if='{{model==1}}' />
    </view>
    <view class="timeBox" catchtap="return" wx:if="{{timeBoxShow}}">
      <van-datetime-picker type="date" value="{{ currentDate }}" bind:confirm="confirBrithday" bind:cancel="onCancel" max-date="{{ maxDate }}" min-date="{{ minDate }}" formatter="{{ formatter }}" />
    </view>
    <view class='relationPicker' catchtap='return' wx:if="{{sexBoxShow}}">
      <van-picker columns='{{ sexList }}' show-toolbar title='性别' bind:cancel='onCancel' bind:confirm='confirSex' value-key='name' />
    </view>
  </van-popup>
</view>