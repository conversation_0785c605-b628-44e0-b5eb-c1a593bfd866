.container{
  background: #F8F8F8;
  padding-bottom: 174rpx;
}

/* 章节标题样式 */
.section-title {
  padding: 20rpx 20rpx 28rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  background: transparent;
}

.van-field__label{
	min-width: 300rpx;
  font-size: 32rpx;
  color: #666666;
}
.van-cell__title{
  font-size: 32rpx;
  height: 56rpx;
  color: #666666;
  display: flex;
}

.van-cell{
  /* padding: 32rpx;
  padding: var(--cell-vertical-padding,32rpx) var(--cell-horizontal-padding,32rpx); */
  display: flex;
  align-items: center;
}
.van-cell__value {
	overflow: auto;
}
.van-cell--hover{
  display: flex;
  align-items: center;
}
input,.info .van-cell__value{
  font-size: 32rpx;
}
.area .van-cell__value{
	max-width: 500rpx;
	overflow-x: auto;
}

.info input.c333{
  color: #333;
}
.info .van-cell:after {
  border-bottom:1px solid #eee;
}
.shade{
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0;
  top: 0;
  background:rgba(0,0,0,0.29);
  z-index: 1000;
}
.shade .timeBox{
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
}
.guardian input{
  color: #333;
  font-size: 32rpx;
}
.relation{
  color: red;
}
.del{
  width: 180rpx;
	height: 80rpx;
	border: 2rpx solid #EEEEEE;
	border-radius: var(--btnRadius);
}
.save{
	flex: 1;
	height: 80rpx;
	border-radius: var(--btnRadius);
}
.info .van-cell__value{
  color: #333;
}

.notice{
  width: 686rpx;
  background: #EEF9FF;
  margin: 0 auto;
  padding-bottom: 22rpx;
  border-radius: 20rpx;
}
.notice image{
  display: block;
  float: left;
  width: 30rpx;
  height: 26rpx;
  margin-left: 24rpx;
  margin-top: 28rpx;
}
.notice view{
  width: 588rpx;
  float: right;
  color: #2B94FF;
  font-size: 28rpx;
  line-height: 40rpx;
  margin-right: 30rpx;
  padding-top: 18rpx;
}
.authbtn{
  /* width: 136rpx; */
  height: 50rpx;
  line-height: 50rpx;
  color: #ffffff;
  background-color: var(--themeColor);
}
