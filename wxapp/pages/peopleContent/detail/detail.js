// pages/peopleContent/detail/detail.js
const check = require('../../../utils/check')
const util = require('../../../utils/util')
const api = require('../../../config/api')
import { startEid } from '../../../mp_ecard_sdk/main'
Page({
  ...check,
  /**
   * 页面的初始数据
   */
  data: {
    inquirerId: null,
    model: null, //成人 还是儿童
    isDetail: true,
    showPicker: false,
    info: {
      maritalStatus: '0'
    },
    // 过敏史
    allergy: {
      checked: false,
      content: ''
    },
    // 既往史
    always: {
      checked: false,
      content: ''
    },
    shade: false,
    currentDate: new Date().getTime(),
    maxDate: new Date().getTime(),
    minDate: new Date(new Date().getFullYear() - 15, new Date().getMonth(), new Date().getDate() + 1).getTime(),
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      if (type === 'day') {
        return `${value}日`
      }
      return value
    },
    relationList: [],
    relationListChild: [
      { relationName: '子女', gender: 0, relationId: 2 },
      { relationName: '其他', gender: 0, relationId: 4 }
    ],
    sexList: [
      { name: '男', id: 1 },
      { name: '女', id: 0 }
    ],
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '',
    isEdit: true, //判断是否可以修改---身份证信息未空可以修改，否则不可以修改
    doctorId: null,
    relationBoxShow: false,
    timeBoxShow: false,
    sexBoxShow: false,
    idCardClick: true,
    flag: true,
    checked: false,
    accessType: '', //'auth'代表是实名认证
    eidToken: '',
    routerPage: 0,
    isRefresh: true
  },
  showAddress(e) {
    wx.hideKeyboard()
    var type = e.currentTarget.dataset.type
    if (type === '1') {
      this.setData({
        relationBoxShow: false,
        timeBoxShow: false,
        sexBoxShow: true
      })
    } else if (type === '2') {
      if (!this.data.idCardClick) {
        return false
      }
      this.setData({
        relationBoxShow: false,
        timeBoxShow: true,
        sexBoxShow: false
      })
    } else if (type === '3') {
      this.setData({
        relationBoxShow: true,
        timeBoxShow: false,
        sexBoxShow: false
      })
    }
    this.setData({
      showPicker: !this.data.showPicker
    })
  },
  getRelation() {
    util.request(api.relationList, {}, 'post')
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            relationList: res.data.data.relationList
          })
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
      .catch(res => {
      })
  },
  // 获取详情
  getDetail() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.peopleDetail, { inquirerId: this.data.inquirerId }, 'post', 2)
      .then(res => {
        var data = res.data.data
        util.hideToast()
        if (res.data.code === 0) {
          // 自动填充收货人信息
          let hasReceiverUpdate = false

          // 如果收货人姓名为空，则自动填充就诊人姓名
          if ((!data.consignee || data.consignee.trim() === '') && data.name && data.name.trim() !== '') {
            data.consignee = data.name.trim()
            hasReceiverUpdate = true
            console.log('编辑页面自动填充收货人姓名:', data.consignee)
          }

          // 如果收货人手机号为空，则自动填充就诊人手机号
          if ((!data.consigneePhone || data.consigneePhone.trim() === '') && data.phone && data.phone.trim() !== '') {
            data.consigneePhone = data.phone.trim()
            hasReceiverUpdate = true
            console.log('编辑页面自动填充收货人手机号:', data.consigneePhone)
          }

          if (hasReceiverUpdate) {
            console.log('编辑页面收货人信息自动填充完成:', {
              consignee: data.consignee,
              consigneePhone: data.consigneePhone
            })
          } else {
            console.log('编辑页面无需自动填充收货人信息，当前状态:', {
              patientName: data.name,
              patientPhone: data.phone,
              consignee: data.consignee,
              consigneePhone: data.consigneePhone
            })
          }

          this.setData({
            info: data,
            currentDate: new Date(data.birthday).getTime(),
            ['allergy.checked']: data.allergy === '' || data.allergy === null ? false : true,
            ['allergy.content']: data.allergy !== null ? data.allergy : '',
            ['always.checked']: data.pastHistory === '' || data.allergy === null ? false : true,
            ['always.content']: data.pastHistory !== null ? data.pastHistory : '',
            isEdit: data.idCard === null || data.idCard === '' ? false : true,
            checked: data.idCard === null || data.idCard === '' ? 0 : 1
          })
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
  },
  onCancel(e) {
    this.setData({
      showPicker: false
    })
  },
  selectRelation() {
    this.setData({
      showPicker: true
    })
  },
  confirRelation(e) {
    this.setData({
      showPicker: false,
      ['info.relation']: e.detail.value.relationId,
      ['info.relationName']: e.detail.value.relationName
    })
  },
  confirBrithday(e) {
    this.setData({
      showPicker: !this.data.showPicker,
      ['info.birthday']: util.getTime(e.detail)
    })
  },
  confirSex(e) {
    this.setData({
      showPicker: !this.data.showPicker,
      ['info.gender']: e.detail.value.id
    })
  },
  // 接受子组件传递过来的选择结果
  propContent: function(event) {
    var data = event.detail.data
    this.setData({
      allergy: data.allergy,
      always: data.always,
      ['info.allergy']: data.allergy.checked ? data.allergy.content : '',
      ['info.pastHistory']: data.always.checked ? data.always.content : ''
    })
  },
  nameChange(e) {
    var inputValue = util.filterEmoji(e.detail)
    this.setData({
      ['info.name']: inputValue,
      // 自动填充收货人姓名
      ['info.consignee']: inputValue
    })
  },
  // 身份证号同步
  idCardChange(e) {
    let birthday = ''
    if (e.detail.length == 18 || e.detail.length == 0) {
      if (this.isValidIdCard(e.detail)) {
        birthday = util.IdCard(e.detail, 1)
      }
      this.setData({
        ['info.birthday']: birthday,
        idCardClick: birthday ? false : true
      })
    }
    this.setData({
      ['info.idCard']: e.detail
    })
  },
  // 文本框内容同步
  inputChange(e) {
    console.log(e, 194)
    var type = e.currentTarget.dataset.type
    var inputValue = util.filterEmoji(e.detail.value)
    let birthday = ''
    if (type === 'idCard' && this.data.model === 1) {
      if (this.isValidIdCard(e.detail.value)) {
        birthday = util.IdCard(e.detail.value, 1)
      }
      this.setData({
        ['info.birthday']: birthday,
        idCardClick: birthday ? false : true
      })
    }
    var value = 'info' + '.' + type
    this.setData({
      [value]: inputValue
    })

    // 自动填充收货人信息
    if (type === 'phone') {
      // 如果填写就诊人手机号，则自动带入收货人手机号
      this.setData({
        'info.consigneePhone': inputValue
      })
    }
  },
  // 保存
  save() {
    if (this.data.info.name.length < 2 || this.data.info.name.length > 6) {
      util.showToast({ 'title': '请输入正确的就诊人姓名' })
      return
    }
    if (this.data.info.childTag === 1) {
      if (!this.isValidNull(this.data.info.name)) {
        util.showToast({ 'title': '请输入就诊人姓名' })
      } else if (!this.isValidNull(this.data.info.birthday)) {
        util.showToast({ 'title': '请选择就诊人出生日期' })
      } else if (!this.isValidNull(this.data.info.guardianName)) {
        util.showToast({ 'title': '请输入监护人姓名' })
      } else if (this.data.info.guardianName.length < 2) {
        util.showToast({ 'title': '请输入争取的监护人姓名' })
      } else if (!this.isValidNull(this.data.info.guardianIdCard)) {
        util.showToast({ 'title': '请输入监护人身份证号码' })
      } else if (!this.isValidIdCard(this.data.info.guardianIdCard)) {
        util.showToast({ 'title': '监护人身份证号码格式不正确' })
      } else if (!this.isValidNull(this.data.info.guardianPhone)) {
        util.showToast({ 'title': '请输入监护人手机号' })
      } else if (!this.isValidPhone(this.data.info.guardianPhone)) {
        util.showToast({ 'title': '监护人手机号格式不正确' })
      } else if (!this.data.info.cityId) {
        util.showToast({ 'title': '请选择现居地区' })
      } else if (!this.data.info.address) {
        util.showToast({ 'title': '请输入详细地址' })
      } else {
        this.EditPeople()
      }
    } else {
      if (!this.data.isEdit) {
        if (!this.isValidNull(this.data.info.name)) {
          util.showToast({ 'title': '请输入就诊人姓名' })
        } else if (!this.isValidNull(this.data.info.idCard)) {
          util.showToast({ 'title': '请输入就诊人身份证号' })
        } else if (!this.isValidIdCard(this.data.info.idCard)) {
          util.showToast({ 'title': '就诊人身份证格式不正确' })
        } else if (!this.isValidNull(this.data.info.phone)) {
          util.showToast({ 'title': '请输入就诊人手机号' })
        } else if (!this.isValidPhone(this.data.info.phone)) {
          util.showToast({ 'title': '就诊人手机号格式不正确' })
        } else if (!this.data.info.cityId) {
          util.showToast({ 'title': '请选择现居地区' })
        } else if (!this.data.info.address) {
          util.showToast({ 'title': '请输入详细地址' })
        } else if (!this.data.info.contactName) {
          util.showToast({ 'title': '请填写紧急联系人姓名' })
        } else if (!this.data.info.contactPhone) {
          util.showToast({ 'title': '请填写紧急联系人手机号' })
        } else if (!this.isValidPhone(this.data.info.contactPhone)) {
          util.showToast({ 'title': '请填写正确紧急联系人手机号' })
        } else {
          this.EditPeople()
        }
      } else {
        if (!this.isValidNull(this.data.info.phone)) {
          util.showToast({ 'title': '请输入就诊人手机号' })
        } else if (!this.isValidPhone(this.data.info.phone)) {
          util.showToast({ 'title': '就诊人手机号格式不正确' })
        } else if (!this.data.info.cityId) {
          util.showToast({ 'title': '请选择现居地区' })
        } else if (!this.data.info.address) {
          util.showToast({ 'title': '请输入详细地址' })
        } else if (!this.data.info.contactName) {
          util.showToast({ 'title': '请填写紧急联系人姓名' })
        } else if (!this.data.info.contactPhone) {
          util.showToast({ 'title': '请填写紧急联系人手机号' })
        } else if (!this.isValidPhone(this.data.info.contactPhone)) {
          util.showToast({ 'title': '请填写正确紧急联系人手机号' })
        } else {
          this.EditPeople()
        }
      }
    }
    return
  },
  EditPeople() {
    if (!this.data.flag) {
      return false
    }
    if (this.data.checked == 0) {
      util.showToast({ 'title': '请阅读并勾选用户协议隐私政策' })
      return
    }
    this.setData({
      flag: false
    })

    // 构建保存参数
    const saveParams = { ...this.data.info }

    // 收货人字段已经是正确的字段名，无需映射

    if (this.data.doctorId && !this.data.isEdit) {
      saveParams.doctorId = this.data.doctorId
      saveParams.isFirstUpdate = 1
    }

    // 添加视频咨询相关参数
    if (this.data.holderId) {
      saveParams.holderId = this.data.holderId
    }
    if (this.data.subOrderCode) {
      saveParams.subOrderCode = this.data.subOrderCode
    }
    if (this.data.packageCode) {
      saveParams.packageCode = this.data.packageCode
    }

    console.log('保存就诊人信息参数:', {
      inquirerId: saveParams.inquirerId,
      holderId: saveParams.holderId,
      subOrderCode: saveParams.subOrderCode,
      packageCode: saveParams.packageCode,
      name: saveParams.name,
      consignee: saveParams.consignee,
      consigneePhone: saveParams.consigneePhone
    })

    util.request(api.addPeople, saveParams, 'post', 2)
      .then(res => {
        if (res.data.code === 0) {
          const { eidToken, faceFlag } = res.data.data
          if (faceFlag) {
            this.data.eidToken = eidToken
            this.goStartEid()
          } else {
            util.showToast({
              title: '保存成功',
              icon: 'success',
              duration: 1000
            })

            // 如果是从视频咨询页面跳转过来的，设置全局标识以便返回时刷新数据
            if (this.data.holderId || this.data.subOrderCode || this.data.packageCode) {
              const app = getApp()
              if (app.globalData) {
                app.globalData.needRefreshVideoConsultPatientList = true
                console.log('编辑患者成功，设置视频咨询页面刷新标识')
              }
            }

            this.goback()
          }
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
        this.setData({
          flag: true
        })
      })
      .catch(res => {
        console.error('保存就诊人信息失败:', res)
        this.setData({
          flag: true
        })
      })
  },
  // 删除
  del(e) {
    var id = this.data.info.inquirerId
    util.showModal({
      content: '就诊人删除后不可恢复，历史就诊记录 还会保留，您确认删除吗？',
      showCancel: true,
      cancelText: '取消',
      cancelColor: '#666666',
      confirmText: '确认',
      success: (res) => {
        if (!res.cancel) {
          util.request(api.delPeople, { inquirerId: id }, 'post', 2)
            .then(res => {
              if (res.data.code === 0) {
                util.showToast({
                  title: '删除成功'
                })
                this.goback()
              } else {
                util.showToast({ 'title': res.data.msg })
              }
            })
            .catch(res => {
            })
        }
      }
    })
  },
  onChange(event) {
    this.setData({
      checked: event.detail ? 1 : 0
    })
  },
  goAgreement(e) {
    const { type } = e.currentTarget.dataset
    this.data.isRefresh = false
    wx.navigateTo({
      url: '/pages/agreement/index?type=' + type
    })
  },
  onMaritalChange(e) {
    this.setData({
      'info.maritalStatus': e.detail.value
    })
  },
  onPickerConfim(options) {
    const { areaId } = options.detail
    this.setData({
      'info.cityId': areaId[2]
    })
    console.log(options, 'onPickerConfim')
  },
  onInputVal(e) {
    const val = e.detail
    const { key } = e.currentTarget.dataset
    this.setData({
      [`info.${key}`]: util.filterEmoji(val)
    })
    console.log(this.data.info)
  },
  // 人脸核身
  async goStartEid() {
    const that = this
    startEid({
      data: {
        token: that.data.eidToken
      },
      verifyDoneCallback(res) {
        const { token, verifyDone } = res
        console.log('收到核身完成的res:', res)
        console.log('核身的token是:', token)
        console.log('是否完成核身:', verifyDone)
        if (verifyDone) {
          that.inquirerFace(that.data.eidToken)
        }
      }
    })
  },
  async inquirerFace(eidToken) {
    try {
      const that = this
      const { data } = await util.request(`${api.inquirerFace}?eidToken=${eidToken}`, {
        eidToken
      }, 'post')
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
        return
      }

      // 如果是从视频咨询页面跳转过来的，设置全局标识以便返回时刷新数据
      if (that.data.holderId || that.data.subOrderCode || that.data.packageCode) {
        const app = getApp()
        if (app.globalData) {
          app.globalData.needRefreshVideoConsultPatientList = true
          console.log('编辑患者人脸核身成功，设置视频咨询页面刷新标识')
        }
      }

      if (that.data.accessType) {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      } else {
        that.goback()
      }
      console.log(eidToken, 'eidToken', data, '==================================inquirerFace==================================')
    } catch (error) {
      throw new Error(error)
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    var pages = getCurrentPages()
    this.setData({
      model: options.model * 1,
      inquirerId: options.inquirerId,
      doctorId: options.doctorId ? options.doctorId : null,
      accessType: options.type ? options.type : '',
      navTitle: options.type ? '实名认证' : '就诊人详情',
      routerPage: pages.length,
      // 新增视频咨询相关参数
      holderId: options.holderId || null,
      subOrderCode: options.subOrderCode || null,
      packageCode: options.packageCode || null,
      consultId: options.consultId || null
    })

    console.log('就诊人详情页面参数:', {
      inquirerId: options.inquirerId,
      holderId: options.holderId,
      subOrderCode: options.subOrderCode,
      packageCode: options.packageCode,
      consultId: options.consultId,
      doctorId: options.doctorId,
      routerPage: this.data.routerPage
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  async onShow() {
    // const userInfo = wx.getStorageSync('userInfo')
    // let response
    // if (!userInfo.userId) {
    //   response = await util.loginByWeixin()
    // }
    // if (response && response.data.code === 0 && response.data.data.loginStatus === 1) {
    //   wx.navigateTo({
    //     url: '/pages/auth/login/login'
    //   })
    // } else {
    //   this.getRelation()
    //   if (this.data.isRefresh) {
    //     this.getDetail()
    //   }
    // }
    this.getRelation()
    if (this.data.isRefresh) {
      this.getDetail()
    }
  },
  getPhoneNumber(data) {
    const userInfo = wx.getStorageSync('userInfo')
    const openId = userInfo.openId
    const params = {
      cloudID: data.detail.cloudID,
      encryptedData: data.detail.encryptedData,
      iv: data.detail.iv,
      openId: openId
    }
    util.request(api.AuthPhone, params, 'post')
      .then(res => {
        console.log(res)
        if (res.data.code === 0) {
          const { data } = res.data
          this.setData({
            'info.phone': data.phoneNumber,
            // 自动填充收货人手机号
            'info.consigneePhone': data.phoneNumber
          })
        } else {
          console.log('拒绝授权')
        }
      })
      .catch(err => {
        console.log(err, '失败了')
      })
  },
  goback() {
    const { routerPage } = this.data
    setTimeout(() => {
      if (routerPage > 1) {
        wx.navigateBack({
          delta: 1
        })
      } else {
        wx.switchTab({
          url: '/pages/home/<USER>'
        })
      }
    }, 1000)
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
