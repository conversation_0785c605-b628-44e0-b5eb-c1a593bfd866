/* pages/people/people.wxss */
page{
  background: #F8F8F8;
}
.item{
	border-radius: 20rpx;
}
.list .item {
  position: relative;
  margin-bottom: 32rpx;
}
.p30 {
  padding: 32rpx 32rpx 0 32rpx;
}
.list .item .label {
  position: absolute;
  right: 0;
  top: 0;
  width: 128rpx;
  height: 50rpx;
  text-align: center;
  line-height: 50rpx;
  background: #e7f4ff;
  border-radius: 0 20rpx 0 20rpx;
  color: #2d95ff;
  font-size: 24rpx;
}
.list .item .name {
  padding-top: 32rpx;
  padding-bottom: 32rpx;
  line-height: 44rpx;
}
.list .item .btn {
  height: 85rpx;
  box-sizing: border-box;
  border-top: 1rpx solid #eeeeee;
}
.list .item .btn view {
  float: right;
  margin-left: 60rpx;
  height: 84rpx;
  line-height: 84rpx;
  font-size: 28rpx;
  color: #666666;
}
.list .item .btn view image {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  vertical-align: top;
  margin-top: 22rpx;
  margin-right: 8rpx;
}
.isRz {
  position: absolute;
  top: 124rpx;
  right: 32rpx;
  width: 136rpx;
  height: 56rpx;
  color: #fff;
  background: #ffbb6c;
  font-size: 24rpx;
  border-radius: 4rpx;
  /* box-sizing: border-box;
  padding-left: 16rpx; */
  line-height: 56rpx;
  text-align: center;
}
.isRz image {
  display: inline-block;
  width: 14rpx;
  height: 32rpx;
  vertical-align: top;
  margin-top: 12rpx;
}
