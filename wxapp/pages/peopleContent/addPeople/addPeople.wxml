<view class="container">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="bg-color-white pt30">
		<view class="notice  clearfix">
			<image src="../../../static/images/ic_notice_01.png"></image>
			<view>根据相关政策，就诊人姓名、证件信息平台审核，不会外传，请输入正确的就诊人信息。</view>
		</view>
	</view>
	<view class="bg-color-white">
		<view class="f32 b pt30 pl30 " style="padding-bottom:8rpx">
			就诊人信息
		</view>
		<view class="inputBox pl30 pr30">
			<view class="item w100 bb1">
				<input type="text" class="pt30 pb30 f32 c333" value="{{info.name}}" bindinput='inputChange' data-type="name"
					placeholder-style="color:#B4B4B4" placeholder="就诊人姓名" />
			</view>
			<view class="item w100 bb1">
				<input type="idcard" class="pt30 pb30 f32 c333" bindinput="idCardChange" data-type="idCard" placeholder-style="color:#B4B4B4" maxlength="18"
					placeholder="就诊人身份证号{{info.childTag==1?'（选填）':''}}" />
			</view>
			<block wx:if="{{info.childTag==1}}">
				<view class="item w100 bb1 sex clearfix">
					<view class="f24 c999 fl">就诊人性别</view>
					<view class="fl" style="margin-left:74rpx">
						<radio-group class="radio" bindchange="radioChange">
							<view class="dib mr30 chooseItem">
								<radio value="1" checked="{{info.gender==1}}"><text class="f32 ml10">男</text></radio>
							</view>
							<view class="dib mr30 chooseItem">
								<radio value="0" checked="{{info.gender==0}}"><text class="f32 ml10">女</text></radio>
							</view>
						</radio-group>
					</view>
				</view>
				<view class="item w100 rel" bindtap="showAddress">
					<input type="text" class="pt30 pb30 f32 c333 w100" placeholder-style="color:#B4B4B4" placeholder="就诊人出生日期"
						value="{{info.birthday}}" disabled />
					<view class="icon">
						<image src="{{imgObject.ic_more_grey}}" class="imgBlock"></image>
					</view>
				</view>
			</block>
			<view class="item w100" wx:else>
				<input class="pt30 pb30 f32 c333" type="number" bindblur="inputChange" data-type="phone"
					placeholder-style="color:#B4B4B4" maxlength="11" placeholder="就诊人手机号码" />
			</view>
		</view>
	</view>
	<view class="mt20 bg-color-white " wx:if="{{info.childTag==1}}">
		<view class="f32 b pt30 pl30 pb10">紧急联系人(法定关系)</view>
		<view class="inputBox pl30 pr30">
			<view class="item w100 bb1">
				<input type="text" class="pt30 pb30 f32 c333" placeholder-style="color:#B4B4B4"
					placeholder="请输入紧急联系人姓名" bindinput="inputChange" value="{{info.guardianName}}" data-type="guardianName" />
			</view>
			<!-- <view class="item w100 bb1">
				<input type="idcard" class="pt30 pb30 f32 c333" placeholder-style="color:#B4B4B4" placeholder="请输入紧急联系人身份证号"
					bindblur="inputChange" maxlength="18" value="{{info.guardianIdCard}}" data-type="guardianIdCard" />
			</view> -->
			<view class="item w100 ">
				<input type="number" class="pt30 pb30 f32 c333" placeholder-style="color:#B4B4B4" placeholder="请输入紧急联系人手机号"
					bindblur="inputChange" maxlength="11" value="{{info.guardianPhone}}" data-type="guardianPhone" />
			</view>
		</view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="f32 b pb40">与您的关系</view>
		<view class="relation" wx:if="{{info.childTag==0}}">
			<text wx:for="{{relationList}}" wx:key="{{index}}" data-id="{{item.relationId}}"
				class="{{item.relationId==info.relation?'cur':''}}" bindtap="relationChange">{{item.relationName}}</text>
		</view>
		<view class="relation" wx:else>
			<text wx:for="{{relationListChild}}" wx:key="{{index}}" data-id="{{item.relationId}}"
				class="{{item.relationId==info.relation?'cur':''}}" bindtap="relationChange">{{item.relationName}}</text>
		</view>
	</view>
	<view class="mt20 bg-color-white">
		<view class="f32 b p30 pb0">收货地址</view>
		<view class="item bb1" style="padding-left: 30rpx; padding-right: 30rpx;">
			<input type="text" class="pt25 pb25 f32 c333" value="{{info.consignee}}" bindinput='inputChange' data-type="consignee"
				placeholder-style="color:#B4B4B4" placeholder="收货人姓名" />
		</view>
    	<view class="item bb1" style="padding-left: 30rpx; padding-right: 30rpx;">
			<input type="number" class="pt25 pb25 f32 c333" value="{{info.consigneePhone}}" bindinput='inputChange' data-type="consigneePhone"
				placeholder-style="color:#B4B4B4" placeholder="收货人手机号" maxlength="11" />
		</view>
		<AddresPicker areaValue='{{info.addressPrefix}}' placeholder='选择所在省市区' bind:onPickerConfim='onPickerConfim'>
		</AddresPicker>
		<van-field value='{{ info.address }}' input-align='left' placeholder='请输入街道/门牌号等详细地址' bind:input='onInputVal'
			data-key='address' />
	</view>
	<view class='bg-color-white mt20' wx:if="{{info.childTag!==1}}">
		<view class="f32 b p30 pb0">紧急联系人(法定关系)</view>
		<van-field value='{{ info.contactName }}' input-align='left' placeholder='请输入紧急联系人姓名' data-key='contactName'
			bind:input='onInputVal' />
		<van-field value='{{ info.contactPhone }}' type='number' maxlength="11" input-align='left'
			placeholder='请输入紧急联系人手机号码' data-key='contactPhone' bind:input='onInputVal' />
	</view>
	<view class='bg-color-white mt20 p30' wx:if="{{info.childTag!==1}}">
		<view class="clearfix">
			<text class="f32 b">婚姻状况</text>
			<radio-group class="radio" bindchange="onMaritalChange">
				<view class="dib ml30 chooseItem">
					<radio value="0" checked="{{info.maritalStatus=='0'}}">
						<text class="f32 ml10">未婚</text>
					</radio>
				</view>
				<view class="dib ml30 chooseItem">
					<radio value="1" checked="{{info.maritalStatus=='1'}}">
						<text class="f32 ml10">已婚</text>
					</radio>
				</view>
			</radio-group>
		</view>
	</view>
	<medicalHistory allergy="{{allergy}}" isDetail="{{isDetail}}" always="{{always}}" bind:propContent="propContent">
	</medicalHistory>
	<view class="confir mt50 p30 pt30 ">
		<button bindtap="save">保存</button>
	</view>
	<view class="tc pb30">
		<text class="f28 color-primary" bindtap="switchPeople">
			<block wx:if="{{info.childTag==0}}">儿童就诊请点击此处</block>
			<block wx:else>返回成人就诊请点击此处</block>
		</text>
	</view>
</view>
<van-popup show="{{showPicker}}" bind:close='showAddress' close-on-click-overlay='{{true}}' round='{{true}}'
	position="bottom">
	<!-- 选择年月日组件 -->
	<view class="timeBox" catchtap="return">
		<van-datetime-picker type="date" value="{{ currentDate }}" bind:confirm="onInput" bind:cancel="onCancel"
			max-date="{{ maxDate }}" min-date="{{ minDate }}" formatter="{{ formatter }}" />
	</view>
</van-popup>