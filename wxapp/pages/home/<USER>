<view id="container" class="container rel bg-color-gray-light" style="background-image: url('{{imgObject.bg_home}}'); background-repeat: no-repeat; background-size: contain;">
	<!-- URL scheme中转页面 -->
	<view wx:if="{{showRedirectPage}}" class="redirect-overlay">
		<view class="redirect-content">
			<view class="redirect-icon">
				<image src="{{imgObject.ic_video_consultation}}" class="redirect-image" mode="aspectFit"></image>
			</view>
			<view class="redirect-title">正在为您跳转到视频咨询</view>
			<view class="redirect-subtitle">{{redirectCountdown}}秒后自动跳转</view>
			<view class="redirect-params">
				<text class="param-label">订单编号：</text>
				<text class="param-value">{{redirectParams.subOrderCode}}</text>
			</view>
			<view class="redirect-params">
				<text class="param-label">套餐编号：</text>
				<text class="param-value">{{redirectParams.packageCode}}</text>
			</view>
			<button class="redirect-btn" bindtap="handleImmediateRedirect">立即跳转</button>
		</view>
	</view>

	<navbar isBack="{{isBack}}"></navbar>
	<view class="logo" style="height:{{logoHeight}}px;top:{{statusBarHeight}}px;line-height:{{logoHeight}}px;">
		<!-- <image src="{{imgObject.logo}}" mode="heightFix"></image> -->
		 <text style="color: #333; font-weight: 600; font-size: 36rpx">掌上健康</text>
	</view>
	<view class="rel">
		<!-- 搜索框 -->
		<view class="p20 rel search">
			<view class="w100 rel" bindtap="goSearchPage">
				<view class="icon">
					<image src="{{imgObject.search}}" class="imgBlock"></image>
				</view>
				<input type="text" class="w100" disabled placeholder="医生姓名、医院名称" placeholder-class="c999" class="f28 c999" />
			</view>
		</view>
		<!-- 快速功能按钮 -->
		<view class="quick-functions pl20 pr20 mt20">
			<view class="function-item" bindtap="handleQuickConsult">
				<image src="{{imgObject.ic_quick_consultation}}" class="function-icon" mode="aspectFit"></image>
				<text class="function-text">快速咨询</text>
				<text class="function-text-desc">海量名医 对症咨询</text>
			</view>
			<view class="function-item" bindtap="handleDepartmentNavigation">
				<image src="{{imgObject.ic_department_navigation}}" class="function-icon" mode="aspectFit"></image>
				<text class="function-text">科室导航</text>
				<text class="function-text-desc">科室筛选 高效复诊</text>
			</view>
			<view class="function-item">
				<button
					wx:if="{{isLoggedIn && !hasPhone}}"
					class="function-btn"
					open-type="getPhoneNumber"
					bindgetphonenumber="getPhoneNumber"
					catch:tap="handleVideoConsult"
				>
					<image src="{{imgObject.ic_video_consultation}}" class="function-icon" mode="aspectFit"></image>
					<text class="function-text">全科视频问诊</text>
					<text class="function-text-desc">全科医生 实时接诊</text>
				</button>
				<view
					wx:else
					bindtap="handleVideoConsult"
					style="display: flex; flex-direction: column; align-items: center;"
				>
					<image src="{{imgObject.ic_video_consultation}}" class="function-icon" mode="aspectFit"></image>
					<text class="function-text">全科视频问诊</text>
					<text class="function-text-desc">全科医生 实时接诊</text>
				</view>
			</view>
			<!-- <view class="function-item">
				<button
					wx:if="{{isLoggedIn && !hasPhone}}"
					class="function-btn"
					open-type="getPhoneNumber"
					bindgetphonenumber="getPhoneNumber"
					catch:tap="handleHospitalizationGuidance"
				>
					<image src="{{imgObject.ic_hospitalization_guidance}}" class="function-icon" mode="aspectFit"></image>
					<text class="function-text">小诺导诊</text>
					<text class="function-text-desc">为您解决 挂什么科</text>
				</button>
				<view
					wx:else
					bindtap="handleHospitalizationGuidance"
					style="display: flex; flex-direction: column; align-items: center;"
				>
					<image src="{{imgObject.ic_hospitalization_guidance}}" class="function-icon" mode="aspectFit"></image>
					<text class="function-text">小诺导诊</text>
					<text class="function-text-desc">为您解决 挂什么科</text>
				</view>
			</view> -->
		</view>

		<!-- 轮播图 -->
		<view class="banner rel" wx:if="{{bannerList.length > 0}}">
			<swiper indicator-dots="{{indicatorDots}}" autoplay="{{autoplay}}" interval="{{interval}}" duration="{{duration}}"
				indicator-color="{{indicatorColor}}" indicator-active-color="{{indicatorActiveColor}}" circular="{{circular}}"
				bindchange="swiperChange">
				<block wx:for="{{bannerList}}" wx:key="index" > 
					<swiper-item wx:if="{{item.bannerType === 'AI'}}">
						<button
							wx:if="{{isLoggedIn && !hasPhone}}"
							class="banner-btn"
							open-type="getPhoneNumber" 
							bindgetphonenumber="getPhoneNumber"
							data-type="{{item.bannerType}}" 
							data-id="{{item.id}}"
							data-url="{{item.targetUrl}}" 
							catch:tap="handleDetail"
						>
							<image src="{{item.bannerUrl}}" mode="widthFix" class="imgBlock" />
						</button>
						<image
							wx:else 
							src="{{item.bannerUrl}}" 
							mode="widthFix" 
							class="imgBlock" 
							data-type="{{item.bannerType}}" 
							data-id="{{item.id}}"
							data-url="{{item.targetUrl}}" 
							catch:tap="handleDetail"
						/>
					</swiper-item>
					<swiper-item wx:else data-type="{{item.bannerType}}" data-id="{{item.id}}" data-url="{{item.targetUrl}}" catch:tap="handleDetail">
						<image src="{{item.bannerUrl}}" mode="widthFix" class="imgBlock" />
					</swiper-item>
				</block>
			</swiper>
			<view class="dots">
				<block wx:for="{{bannerList}}" wx:key="index">
					<view class="dot{{index == swiperCurrent ? ' active' : ''}}"></view>
				</block>
			</view>
		</view>
		<!-- 公告 -->
		<navigator url="/pages/noticeList/index" class="ml20 mt20 mr20 p20 flex_lr_m bg-color-blue-light container-radius" wx:if='{{noticeList.length}}'>
			<view class="flex_m">
				<image src="{{imgObject.ic_font_message}}" style="width:40rpx;height:40rpx" />
				<view class="c333 f28 bl1 ml20 pl20 flex1 ell_more ell_1 h40" style="height:40rpx">
					{{noticeList[0].title}}
				</view>
			</view>
			<view class="rigth-icon c999">
			</view>
		</navigator>
		<!-- 热门科室 -->
		<view class="bg-color-gray pt20 pb20 m20 container-radius">
			<view class="pl20 pr20 f36 c333 b">
				热门科室
			</view>
			<view class="flex_m mt20">
				<navigator wx:for="{{departments}}" wx:key="index" class="flex1 flex_line_c" open-type='navigate'
					url="/pages/famousDoctor/famousDoctor?departmentId={{item.id}}&departmentName={{item.text}}">
					<image class="w60 h60" src="{{item.default}}" binderror="onImageError" mode="aspectFill" />
					<view class="f28 c666 mt5">
						{{item.text}}
					</view>
				</navigator>
				<navigator open-type='navigate' url="/pages/famousDoctor/famousDoctor" class="flex1 flex_line_c">
					<image class="w60 h60" src="../../static/images/ic_home_all.png"  mode="aspectFill" />
					<view class="f28 c666 mt5">
						全部
					</view>
				</navigator>
			</view>
		</view>

		<!-- 常见疾病 -->
		<view class="bg-color-gray pt20 pb20 m20 container-radius" wx:if="{{commonDiseaseList.length > 0}}">
			<view class="flex_lr_m">
				<view class="pl20 pr20 f36 c333 b">
					常见疾病
				</view>
				<navigator open-type="navigate" url="/pages/famousDoctor/famousDoctor" class="fl_align mr20">
					<view class="mr10 more-text">全部疾病</view>
					<view class="rigth-icon c999 mt1">
					</view>
				</navigator>
			</view>
			<view class="dis_container mt20 pl20">
				<block wx:for="{{commonDiseaseList}}" wx:key="index">
					<text class="item mr20 mb20" bindtap="handleDiseaseNavigator" data-diseaseId="{{item.id}}" data-diseaseName="{{item.name}}">{{item.name}}</text>
				</block>
			</view>
			  
			  
		</view>
		  
		<view class="pb10">
			<!-- fixed header -->
			<van-sticky bind:scroll='onNavScroll'>
				<view class="{{isFixed?'bg-color-white zx999 pb20':''}}">
					<view wx:if='{{isFixed}}' style="height:{{statusBarHeight}}px;"></view>
					<view wx:if='{{isFixed}}' class="fixedLogo" style="height:{{logoHeight}}px;">
						<image src="{{imgObject.logo}}" mode="heightFix" lazy-load="true"></image>
					</view>
					<view class="pl40 pr30 flex_m pt10 pb10">
						<view class="mr30 rel flex_tb_c {{index == currentNav ? 'navActive f36 b':'f34 c999 b500'}}" wx:for='{{navTabList}}'
							wx:key="index" data-index='{{index}}' catchtap="handleSwatchNav">
							{{item.title}}
              <image wx:if='{{index == currentNav}}' src="../../static/images/ic_selected.png" mode=""/>
						</view>
					</view>
				</view>
				<scrollTabs id="scrollTabs" wx:if='{{currentNav==1 && isLogin}}' bind:onSwatchTab='onSwatchTab'></scrollTabs>
			</van-sticky>
			<!-- content -->
			<block wx:if="{{isLogin}}">
				<doctorList wx:if='{{currentNav==0}}' list='{{list}}' type='{{3}}'></doctorList>
				<articleList wx:if='{{currentNav==1}}' list='{{artList}}' id='article' sticky="true"></articleList>
			</block>
			<block wx:else>
				<view class="noData">
					<image src="{{imgObject.img_blank_doctor}}" class="mt0"></image>
					<view class="tc c666 f28 pb40">登录后查看更多</view>
					<view class="login f28" bindtap="handleLogin">立即登录</view>
					<!-- <navigator url='/pages/addEval/addEval?doctorId=212&consultType=1&consultSessionId=1531&type=1'>去评价</navigator>  -->
					<!-- <navigator url='/pages/consult/chat/chat?doctorId=212'>进入诊室</navigator>  -->
					<!-- <navigator url='/pages/famousDoctorDetail/famousDoctorDetail?type=3&doctorId=245'>医生详情</navigator> -->
					<!-- <navigator url='/pages/diseaseDetail/diseaseDetail?source=1&doctorId=1&type=1'>实名认证</navigator>  -->
				</view>
			</block>
		</view>
	</view>
	<van-dialog
	  use-slot
	  title="授权手机号"
	  show="{{ showGetPhone }}"
	  showConfirmButton="{{false}}"
	  showCancelButton="{{false}}"
	>
	  <view class="phone-dialog-content">
	    <view class="phone-tips">为了更好的服务体验，需要授权手机号</view>
	    <button 
	      class="getPhoneBtn"
	      open-type="getPhoneNumber" 
	      bindgetphonenumber="getPhoneNumber"
	    >
	      获取手机号
	    </button>
	  </view>
	</van-dialog>
</view>