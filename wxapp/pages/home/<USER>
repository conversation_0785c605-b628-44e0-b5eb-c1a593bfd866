/* pages/home/<USER>/

/* 首页背景图样式 */

.logo {
  width: 500rpx;
  position: absolute;
  left: 20rpx;
  display: flex;
  align-items: center;
}

.logo image {
  height: 40rpx;
}

.bgImg {
  position: fixed;
  left: 0;
  top: 0;
  height: 498rpx;
  width: 100%;
}

.bgImg image {
  display: block;
  width: 100%;
  height: 100%;
}

.search .rel {
  border: 1rpx solid #dfdfdf;
  height: 80rpx;
  background-color: #fff;
  border-radius: 40rpx;
}

.search .rel input {
  display: block;
  width: 100%;
  height: 100%;
  line-height: 80rpx;
  padding-left: 72rpx;
  box-sizing: border-box;
}

.search .icon {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  left: 20rpx;
  top: 19rpx;
}

.menuItem {
  width: 344rpx;
  height: 160rpx;
}

.banner {
  margin: 0 auto;
  width: 710rpx;
  height: 240rpx;
}
swiper {
  width: 710rpx;
  height: 240rpx;
}
swiper-item {
  border-radius: 8rpx;
}

.dots {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 20rpx;
  display: flex;
  justify-content: center;
}

.dots .dot {
  margin: 0 8rpx;
  width: 10rpx;
  height: 10rpx;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  transition: all 0.6s;
}

.dots .dot.active {
  background: #2e9cff;
  width: 20rpx;
  height: 10rpx;
  border-radius: 6rpx;
  background: #ffffff;
}

.auth-content {
  width: 614rpx;
  border-radius: 20rpx;
  background-color: #fff;
  z-index: 99999;
  padding: 30rpx 0;
}

.container .van-popup {
  border-radius: 10px;
  border-radius: var(--popup-round-border-radius, 10px);
  position: fixed;
  box-sizing: border-box;
  max-height: 100%;
  overflow-y: auto;
  transition-timing-function: ease;
  -webkit-animation: ease both;
  animation: ease both;
  -webkit-overflow-scrolling: touch;
  background-color: #fff;
  background-color: var(--popup-background-color, #fff);
}

.auth-btn {
  background: var(--themeColor);
  margin: 10rpx 20rpx 0 20rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.login {
  margin: 0 auto;
  width: 272rpx;
  height: 84rpx;
  text-align: center;
  line-height: 84rpx;
  background: var(--themeColor);
  color: #fff;
  margin-bottom: 40rpx;
  border-radius: var(--btnRadius);
}

.noData image {
  display: block;
  width: 380rpx;
  height: 316rpx;
  margin: 0 auto;
  margin-top: 50rpx;
}
.bl1 {
  height: 20rpx;
  border-left: 1rpx solid #dddddd;
}

.navActive {
  color: #333;
  font-weight: bold;
}
.navActive image {
  width: 36rpx;
  height: 12rpx;
  position: absolute;
  bottom: -12rpx;
}

.fixedLogo {
  width: 500rpx;
  display: flex;
  align-items: center;
}
.fixedLogo image {
  width: 500rpx;
  height: 40rpx;
  margin-left: 20rpx;
}
.bg-color-gray {
  background: #fdfdfd;
}

.more-text {
  color: #8b8b8b;
  font-size: 24rpx;
}

.fl_align {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mt28 {
  margin-top: 28rpx;
}

.dis_container {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  height: 140rpx;
  overflow: hidden;
}

.item {
  font-size: 28rpx;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #4285ff;
  padding: 8rpx 16rpx;
  background: #ffffff;
  border-radius: 4rpx;
  border: 1rpx solid #367dff;
  max-width: 200rpx;
  height: fit-content;
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 超出部分显示省略号 */
}

.mt1 {
  margin-top: 2rpx;
}

.mr20 {
  margin-right: 20rpx;
}

.phone-dialog-content {
  padding: 30rpx;
  text-align: center;
}

.phone-tips {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 30rpx;
}

.getPhoneBtn {
  width: 80%;
  height: 80rpx;
  line-height: 80rpx;
  background: #2e9cff;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.banner-btn {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  border: none;
  background: none;
}

.banner-btn::after {
  border: none;
}

/* 快速功能按钮样式 */
.quick-functions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.function-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20rpx 10rpx;
  border-radius: 12rpx;
  margin: 0 10rpx;
  transition: all 0.3s ease;
}

.function-item:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.8);
}

.function-icon {
  width: 96rpx;
  height: 96rpx;
  margin-bottom: 16rpx;
}

.function-text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.function-text-desc {
  font-weight: 400;
  font-size: 20rpx;
  color: #999999;
}

.function-btn {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  border: none;
  background: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.function-btn::after {
  border: none;
}

/* URL scheme中转页面样式 */
.redirect-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.redirect-content {
  width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  padding: 60rpx 40rpx;
  text-align: center;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.redirect-icon {
  margin-bottom: 30rpx;
}

.redirect-image {
  width: 120rpx;
  height: 120rpx;
}

.redirect-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.redirect-subtitle {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
}

.redirect-params {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 26rpx;
}

.param-label {
  color: #999;
  margin-right: 10rpx;
}

.param-value {
  color: #333;
  font-weight: 500;
}

.redirect-btn {
  width: 400rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: #2e9cff;
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  margin-top: 30rpx;
  border: none;
}

.redirect-btn::after {
  border: none;
}

.redirect-btn:active {
  background: #1a7acc;
}
