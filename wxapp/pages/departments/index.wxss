.mr_page {
  background-color: var(--whiteColor);
}
.mr_list {
  background-color: var(--whiteColor);
  height: 100vh;
}
.mr_list .van-tree-select__content {
  flex: 1;
}
.mr_list .van-sidebar-item {
  font-size: 32rpx;
  font-weight: 400;
}
.mr_list .van-tree-select__item {
  font-size: 32rpx;
  font-weight: 400;
  padding: 0 0 0 20px;
}
.mr_list .van-tree-select__nav-item {
  padding: 20rpx 0 20rpx 40rpx;
}
.mr_list .van-sidebar-item--selected {
	color:  var(--themeColor);
  border: none;
  position: relative;
}
 .mr_list .van-sidebar-item--selected::before {
  position: absolute;
  top: 50%;
  left: 0;
	width: 10rpx;
  height: 32rpx;
  border-radius: 0 4rpx 4rpx 0;
  background-color: var(--themeColor);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  content: "";
} 
