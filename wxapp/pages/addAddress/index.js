// pages/addAddress/index.js
var api = require('../../config/api.js')
var util = require('../../utils/util')
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '地址详情',
    addressId: null,
    defaultAddr: false,
    receiver: null,
    phone: null,
    addr: null,
    cityText: '',
    cityId: ''
  },
  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad: function(options) {
    this.setData({
      addressId: options.addressId,
      navTitle: options.addressId ? '地址详情' : '添加地址'
    }, async() => {
      options.addressId && this.getDetail() //获取详情
      if (!options.addressId) {
        var userInfo = await util.getBaseInfo()
        this.setData({
          receiver: userInfo.name,
          phone: userInfo.realPhone
        })
      }

    })
  },
  getDetail() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.addresesDetail + `/${this.data.addressId}`, {}, 'GET')
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            addr: res.data.data.addr,
            rangeId: [res.data.data.provinceId, res.data.data.townId, res.data.data.cityId],
            defaultAddr: res.data.data.defaultAddr == 1 ? true : false,
            id: this.data.addressId,
            phone: res.data.data.phone,
            receiver: res.data.data.receiver,
            cityText: `${res.data.data.province}-${res.data.data.city}-${res.data.data.county}`,
            cityId: res.data.data.cityId
          })
        }
        wx.hideToast()
      })
  },
  saveAddress() {
    const params = {
      addr: this.data.addr,
      cityId: this.data.cityId,
      defaultAddr: this.data.defaultAddr ? 1 : 0,
      id: this.data.addressId,
      phone: this.data.phone,
      receiver: this.data.receiver
    }
    if (!this.data.receiver) {
      util.showToast({
        title: '收货人姓名不为空',
        icon: 'none'
      })
      return
    }
    if (this.data.receiver.length > 6) {
      util.showToast({
        title: '请填写正确的收货人姓名',
        icon: 'none'
      })
      return
    }
    var reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
    if (!reg.test(this.data.phone) || !this.data.phone) {
      util.showToast({
        title: '请输入正确的手机号码',
        icon: 'none'
      })
      return
    }
    if (!this.data.cityId) {
      util.showToast({
        title: '请选择所在地区',
        icon: 'none'
      })
      return
    }
    if (!this.data.addr) {
      util.showToast({
        title: '请输入详细地址',
        icon: 'none'
      })
      return
    }
    util.request(api.addresesSave, params, 'POST')
      .then(res => {
        if (res.data.code === 0) {
          util.showToast({
            title: this.data.addressId ? '修改成功' : '添加成功',
            icon: 'success'
          })
          wx.navigateBack({
            delta: 1
          })
        }
      })
  },
  // 设置默认
  onChange(e) {
    this.setData({
      defaultAddr: e.detail
    })
  },
  onPhoneblur() {
    wx.hideKeyboard()
  },
  onReceiver(e) {
    var detail = util.filterEmoji(e.detail)
    var newDetail = util.filterSymbol(detail)
    this.setData({
      receiver: newDetail ? newDetail : ''
    })
  },
  onaddr(e) {
    this.setData({
      addr: util.filterEmoji(e.detail)
    })
  },
  onaddrblur(e) {
    this.setData({
      addr: util.filterEmoji(e.detail.value)
    })
  },
  onPickerConfim(event) {
    const {
      areaId,
      areaValue
    } = event.detail
    this.setData({
      cityId: areaId[2],
      cityText: areaValue
    })
    console.log(event, 256)
  }
})
