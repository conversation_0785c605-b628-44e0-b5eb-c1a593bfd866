/* pages/doctorDetail/doctorDetal.wxss */
page{
  background: #F9F9F9;
}

.container .bg{
  width: 100%;
  height: 300rpx;
  left: 0;
  top: 0;
}
.main{
  width: 100%;
  position: relative;
  padding-top: 130rpx;
}
.doctorInfo{
  position: relative;
  border-radius: 20rpx 20rpx 0 0;
}
.icon{
  display: inline-block;
  width: 134rpx;
  height: 42rpx;
  vertical-align: top;
  margin-top: 1rpx;
  /* position: absolute;
  right: 0; */
}
.IconRt{
  position: absolute;
  right: 0;
}
.doctorInfo .h3{
  padding-top: 8rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 40rpx;
}
.textBox{
  line-height: 40rpx;
}
.donwIcon{
  width: 32rpx;
  height: 32rpx;
  margin: 10rpx auto;
}
.upIcon{
  transform:rotate(180deg);
  -ms-transform:rotate(180deg); 
  -moz-transform:rotate(180deg); 	
  -webkit-transform:rotate(180deg); 
  -o-transform:rotate(180deg); 	
}
.serverItem{
	flex: 1;
  height: 188rpx;
  background: #fff;
  border-radius: 20rpx;
  position: relative;
}

.serverItemActive{
  border: 2rpx solid #367DFF;
  background: #F5FAFF;
}
.serverItem .icon{
  position: absolute;
  right: -1rpx;
  bottom: -1rpx;
  width: 44rpx;
  height: 44rpx;
}
.eval{
	border-radius: 20rpx;
}
.eval .title{
  height: 46rpx;
  line-height: 46rpx;
}
.more{
  color: #8B8B8B;
  font-size: 24rpx;
  line-height: 46rpx;
  height: 46rpx;
}
.more image{
  display: block;
  float: right;
  width: 20rpx;
  height: 46rpx;
}
.eval .list .item{
  margin-top: 32rpx;
}
.photo{
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  overflow: hidden;
}
.eval  .name{
  padding-left: 16rpx;
}
.star image{
  width: 28rpx;
  height: 28rpx;
  margin-left: 12rpx;
}
.eval  .p96{
  padding-left: 96rpx;
}
.doctorMain{
  border-radius: 0;
  padding-top: 0px;
	margin-top: 70rpx;
}
.doctorMain .doctorInfo{
	position: relative;
}
.doctorMain .doctorInfo .photo{
  position: absolute;
  width:128rpx;
  height: 128rpx;
  border-radius: 50%;
  left: 20rpx;
  top:-30rpx;
  overflow: hidden;
}
.doctorMain .doctorInfo .icon{
	width: 136rpx;
	height: 52rpx;
	position: absolute;
	top: 20rpx;
	right: 20rpx;
}
.doctorMain .doctorInfo .p168{
  padding-left: 138rpx;
}
.doctorMain .doctorInfo .name{
  padding-top: 44rpx;
}
.doctorMain .doctorInfo .h3 {
  padding-top: 0rpx;
}
.pt44{
  padding-top: 44rpx;
}
.doctorData{
	margin-top: 100rpx;
}
.shadeBox{
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	background: rgba(255, 255, 255, .5);
	z-index: 99;
}
.serverItem-select{
	width: 40rpx;
	height: 40rpx;
	position: absolute;
	bottom: 0;
	right: 0;
}

.wrapper{
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.wrapper .block{
  width: 590rpx;
  background: #fff;
  border-radius: 20rpx;
}
.wrapper .title{
  height: 140rpx;
  position: relative;
}
.bg_authentication{
  width: 100%;
  height: 100%;
  position: absolute;
}
.ic_doctor_authentication{
  width: 184rpx;
  height: 108rpx;
  position: absolute;
  right: 0;
  top: -20rpx;
}
.title .color-primary{
  position: absolute;
  z-index: 9;
  width: 100%;
}

.wrapper-item{
  background: #F2F7FF;
  border-radius: 8rpx;
}
.wrapper-button{
  height: 88rpx;
  border-radius: 44rpx;
}
