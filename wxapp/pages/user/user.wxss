/* pages/user/user.wxss */
page{
  background: #F8F8F8;
}
.bgImg{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 500rpx;
}
.userInfo{
  padding-left: 60rpx;
  padding-top: 30rpx;
}
.userInfo .photo{
  float: left;
  width: 136rpx;
  height: 136rpx;
  border-radius: 50%;
  background: #fff;
  overflow: hidden;
  padding: 4rpx;
}
.userInfo .avatar {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 50%;
}
.orderMenu{
  margin-top: 86rpx;
  display: flex;
  justify-content: space-between;
}

.order-item {
  padding: 30rpx;
  width: calc((100% - 22rpx) / 2);
  background: #fff;
  border-radius: 20rpx;
}

.br20{
  border-radius: 20rpx;
}
.more{
  color: #8B8B8B;
}
.more image{
  display: inline-block;
  width: 20rpx;
  height: 44rpx;
  vertical-align: top;
}
.orderMenu .item{
  width: 25%;
  float: left;
}
.orderMenu .item .icon{
  width: 100%;
  text-align: center;
  width: 50rpx;
  height: 50rpx;
  margin: 0 auto;
  margin-top: 8rpx;
  position: relative;
}
.orderMenu .item .icon text{
  position: absolute;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  background: var(--redColor);
  line-height: 32rpx;
  color: #fff;
  font-size: 22rpx;
  font-weight: bold;
  top: -12rpx;
  left: 32rpx;
}
.orderMenu .item .icon text.cur{
  width: auto;
  padding: 0 8rpx;
  border-radius: 19rpx;
}
.orderMenu .item .icon image{
  display: block;
  width: 50rpx;
  height: 50rpx;
}
.orderMenu .item view.name{
  padding-top: 8rpx;
}
.menu .item .icon{
  float: left;
}
.menu .item  text{
  float: left;
  display: block;
  height: 44rpx;
  margin-left: 16rpx;
}
.menu .item image{
  display: block;
  float: right;
  width: 44rpx;
  height: 44rpx;
}

.w60 {
  width: 60rpx;
}

.h60 {
  height: 60rpx;
}

.w32 {
  width: 32rpx;
}

.h32 {
  height: 32rpx;
}

.mb28 {
  margin-bottom: 28rpx;
}

.flex_jss {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.center {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.b600 {
  font-weight: 600;
}

.p28 {
  padding: 28rpx;
}

.w48 {
  width: 48rpx;
}

.h48 {
  height: 48rpx;
}

.mt12 {
  margin-top: 12rpx;
}

.flex_jf {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}