// pages/recipeDetail/recipeDetail.js
const api = require('../../config/api')
const util = require('../../utils/util')
Page({

  /**
	 * 页面的初始数据
	 */
  data: {
    userInfo: {}
  },

  /**
	 * 生命周期函数--监听页面加载
	 */
  onLoad(options) {
    const userInfo = wx.getStorageSync('userInfo')
    this.setData({
      userInfo
    })
  },
  async handlePay() {
    util.showLoading({
      title: '支付中...'
    })
    const {
      data
    } = await util.request(api.inspectPay, {
      patientId: this.data.userInfo.userId
    }, 'get')
    if (data.code === 0) {
      wx.hideLoading()
      const payParams = data.data
      wx.requestPayment({
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.package,
        signType: payParams.signType,
        paySign: payParams.paySign,
        success: res => {
          console.log(res)
          wx.showToast({
            title: '支付成功',
            icon: 'success',
            duration: 2000
          })
        },
        fail: res => {
          console.log(res)
          wx.showToast({
            title: '支付失败',
            icon: 'none',
            duration: 2000
          })
        }
      })
    } else {
      util.showToast({
        title: data.msg
      })
    }
  },
  async handleRefund() {
    const {
      data
    } = await util.request(api.inspectRefund, {
      patientId: this.data.userInfo.userId
    }, 'get')
    if (data.code === 0) {
      util.showToast({
        title: '退款成功',
        icon: 'success',
        duration: 2000
      })
    } else {
      util.showToast({
        title: data.msg
      })
    }
  },
  /**
	 * 生命周期函数--监听页面初次渲染完成
	 */
  onReady() {

  },

  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow() {

  },

  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide() {

  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload() {

  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom() {

  },

  /**
	 * 用户点击右上角分享
	 */
  onShareAppMessage() {

  }
})
