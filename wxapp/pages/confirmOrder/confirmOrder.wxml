<!--pages/confirmOrder/confirmOrder.wxml-->
<view class="bg-color-gray-light" style="padding-bottom: 140rpx;">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="bg-color-white">
		<navigator open-type="navigate" class="pt40 pb40 pl30 pr30 rel flex_m" hover-class="none"
			url="/pages/address/index?type=2">
			<image style="width:44rpx;height:44rpx" src="{{imgObject.ic_address}}"></image>
			<view wx:if="{{info.shippingInfo!=null}}" class="flex_m">
				<view class="flex1 ml20">
					<view class="f32 c333">{{info.shippingInfo.receiver}} <text class="ml30">{{info.shippingInfo.phone}}</text>
					</view>
					<view class="f28 c333 mt10">{{info.shippingInfo.fullAddress}}</view>
				</view>
			</view>
			<view class="flex_line_c_m flex1" wx:else>
				<view class="f32 c333 b">请选择收货地址</view>
			</view>
			<image style="width:44rpx;height:44rpx;right:30rpx" class="abs" src="{{imgObject.ic_more_black}}"></image>
		</navigator>
	</view>
	<image class='w100' style="height:4rpx; display: block;" src="{{imgObject.img_order_bar}}"></image>
	<view class="p30 mt20 bg-color-white">
		<view class="f28 b">商品信息</view>
		<view class="mt30 bb1">
			<view wx:for="{{info.products}}" wx:key="index" class="flex_m mb20">
				<view class="drug_cover">
					<image src="{{item.icon}}" mode="aspectFill"></image>
				</view>
				<view class="flex_lr ml10 flex1">
					<view class="f28 c333 ml10 flex1">
						<text wx:if="{{item.rx === 1}}" class="tag">Rx</text>{{item.name}}
					</view>
					<view class="flex_tb">
						<view class="f28 c333 flex_c_end">
							¥{{item.salePrice}}
						</view>
						<view class="f28 c999 flex_c_end">
							x{{item.quantity}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="pt30 tr f28 c333 b">
			小计：<text>¥{{info.price.totalAmount}}</text>
		</view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="pb30 f28 c999">配送方式 <text class="fr c333 f28">{{info.shippingMethod}}</text></view>
		<view class="pb30 f28 c999">支付方式 <text class="fr c333 f28">微信支付</text></view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="pb30 f28 c999">商品金额 <text class="fr c333 f28">￥{{info.price.totalAmount}}</text></view>
		<view class="pb30 f28 c999 bb1">运费 <text class="fr c333 f28">￥{{info.price.freight}}</text></view>
		<view class="pt30 tr f28">
			总计：<text class="color-danger b">¥{{info.price.realAmount}}</text>
		</view>
	</view>
	<view class="mt20 bg-color-white p30">
		<view class="mb20 f28 b">订单备注</view>
		<textarea class="remark p20 box_bb f28 container-radius bg-color-gray-light" placeholder="请输入备注信息…"
			placeholder-class="c999" bindinput="changeNote"></textarea>
	</view>
	<view class="p30 f28 c666 lh40">国家法规要求：“除药品质量原因外,药品一经售出,不得退换”。</view>
	<view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir zx999 flex_lr_m">
		<view class="flex_m c333 f24">实付款：<text class="f36 b color-danger">￥{{info.price.realAmount}}</text>
		</view>
		<view class="fixedbutton f26 container-radius" bindtap="orderCreate" disabled="{{info.shippingInfo===null}}">提交订单
		</view>
	</view>
</view>

<view wx:if='{{overlay}}' class="wrapper-overlay">
	{{errMessage}}
</view>