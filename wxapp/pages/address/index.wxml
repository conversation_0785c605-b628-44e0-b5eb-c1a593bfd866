<!--pages/user/address/index.wxml-->
<view class="address-wrapper" style="padding-bottom: 160rpx;">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="" hover-class="none" hover-stop-propagation="false" wx:if='{{dataList.length>0}}'>
		<view class="p30 bg-color-white mt20 pb20" wx:for='{{dataList}}' wx:key="index" bindtap="chooseAddress" data-index="{{index}}">
			<view class="flex_m b">
				<view class="f32 c333" hover-class="none" hover-stop-propagation="false">
					{{item.receiver}}
				</view>
				<view class="f32 c333 ml35" hover-class="none" hover-stop-propagation="false">
					{{item.phone}}
				</view>
			</view>
			<view class="f28 c333 mt5 ell_more ell_2 pb30 bb1" hover-class="none" hover-stop-propagation="false">
				{{item.province}}{{item.county}}{{item.addr}}
			</view>
			<view class="flex_lr_m pt20" hover-class="none" hover-stop-propagation="false">
				<view class="flex_m" hover-class="none" hover-stop-propagation="false" data-item='{{item}}'
					catchtap='selectAddres' data-id='{{index}}'>
					<image slot="icon" class="icon"
						src="{{ item.defaultAddr === 1 ? static.ic_evaluate_selected :static.ic_evaluate_unselected }}" />
					<view class="f28 c666" hover-class="none" hover-stop-propagation="false">
						默认地址
					</view>
				</view>
				<view class="flex_m" hover-class="none" hover-stop-propagation="false">
					<view class="flex_m f28 c666 mr60" catchtap="updateAddress" data-id='{{item.id}}' hover-class="none"
						hover-stop-propagation="false">
						<image class="icon" src="{{static.ic_address_edit}}"></image>
						编辑
					</view>
					<view class="flex_m f28 c666" hover-class="none" data-id='{{item.id}}' catchtap='deleteAddress'
						hover-stop-propagation="false">
						<image class="icon" src="{{static.ic_search_deleted}}"></image>
						删除
					</view>
				</view>
			</view>
		</view>
	</view>
	<view class="flex_line_c no_msg_box" wx:else>
		<image class="no_msg" src="{{static.nomes}}"></image>
		<view class="f28 c666">您还没有收货地址</view>
	</view>
	<view class="fixed-button flex_m bt1">
		<navigator url='/pages/addAddress/index?len={{dataList.length}}' class="bttom-btn">新增收货地址</navigator>
	</view>
</view>