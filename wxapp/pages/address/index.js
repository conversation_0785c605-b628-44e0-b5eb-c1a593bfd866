// pages/user/address/index.js
var api = require('../../config/api')
var util = require('../../utils/util')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '收货地址',
    dataList: [],
    current: 0,
    type: null,
    static: {
      nomes: api.ImgUrl + 'images/nomes.png',
      ic_evaluate_selected: api.ImgUrl + 'images/ic_evaluate_selected.png',
      ic_evaluate_unselected: api.ImgUrl + 'images/ic_evaluate_unselected.png',
      ic_search_deleted: api.ImgUrl + 'images/ic_search_deleted.png',
      ic_address_edit: api.ImgUrl + 'images/ic_address_edit.png'
    }
  },
  // 选择收货地址
  chooseAddress(e) {
    if (this.data.type === 2) {
      const index = e.currentTarget.dataset.index
      const info = this.data.dataList[index]
      const data = {
        shippingInfoId: info.id,
        fullAddress: info.province + info.city + info.county + info.addr,
        receiver: info.receiver,
        phone: info.phone
      }
      var pages = getCurrentPages()
      console.log(pages, 'pages')
      var prevPage = pages[pages.length - 2]
      prevPage.setData({
        ['info.shippingInfo']: data,
        shippingInfoId: info.id
      })
      wx.navigateBack({
        delta: 1
      })
    }
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      type: options.type * 1 //判断是个人中心进入还是购买药品进入 个人中心1  其他2
    })
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    this.getAddressList()
  },
  selectAddres(e) {
    var item = e.currentTarget.dataset.item
    if (item.defaultAddr === 1) {
      return
    }
    const params = item
    params.defaultAddr = 1
    util.showModal({
      content: '确定设置为默认地址?',
      success: (res) => {
        if (res.confirm) {
          util.request(api.addresesSave, params, 'POST')
            .then(res => {
              if (res.data.code === 0) {
                util.showToast({
                  title: '设置成功',
                  icon: 'success',
                  duration: 2000
                })
                this.getAddressList()
              }
            })
        } else if (res.cancel) {
          // 用户取消
        }
      }
    })
  },
  //获取收货地址列表
  getAddressList() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.addresesList, {
      userAddress: 1
    }, 'GET')
      .then(res => {
        wx.hideToast()
        const result = res.data.data
        if (res.data.code === 0) {
          this.setData({
            dataList: result
          })
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(err => {
      })
  },
  //编辑收货地址
  updateAddress(e) {
    var id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: '/pages/addAddress/index?addressId=' + id
    })
  },
  //删除收货地址
  deleteAddress(e) {
    const id = e.currentTarget.dataset.id
    util.showModal({
      content: '确定要删除当前地址?',
      success: (res) => {
        if (res.confirm) {
          util.request(`${api.addresesDelete}/${id}`, {}, 'POST')
            .then(res => {
              if (res.data.code === 0) {
                util.showToast({
                  title: '删除成功',
                  icon: 'success',
                  duration: 2000
                })
                this.getAddressList()
              }
            })
        } else if (res.cancel) {
        }
      }
    })
  }
})
