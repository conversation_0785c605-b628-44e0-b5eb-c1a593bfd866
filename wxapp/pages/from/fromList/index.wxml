<navbar isBack="{{true}}" isWhite='true' titleColor='#fff' backgroundColor='#367DFF' navTitle="随访任务"></navbar>
<view class="fixed w100 flex_line_c rel">
	<view class="tabbar bg-color-white container-radius flex_m c999">
		<view wx:for='{{tabList}}' bindtap='handleSwitchTab' data-status='{{index}}'
			class="tabbar-item f28 flex_c_m flex1 {{listQuery.status == index  ? 'tabbar-item-active b':''}}">
			{{item}}
		</view>
	</view>
	<import src="../template/swiper.wxml"></import>
	<template is="swiper" data="{{list,listQuery,currentNum,static}}"></template>
</view>