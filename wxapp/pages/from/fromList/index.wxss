page {
  background: #367dff;
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	overflow: hidden;
}
.tabbar {
  width: 522rpx;
  height: 76rpx;
  margin-top: 66rpx;
	padding: 0 5rpx;
	border-radius: 20rpx;
}
.tabbar-item {
	height: 64rpx;
	border-radius: 20rpx;
}
.tabbar-item-active {
  background: #E0EBFF;
  border-radius: 16rpx;
	color: #367dff;
}
swiper {
	height: 912rpx;
	border-radius: 20rpx;
}
swiper-item{
	display: flex;
  align-items: center;
	justify-content: center;
}
.swiper-box {
  width: 100%;
  position: absolute;
  top: 242rpx;
  z-index: 9;
}

.swiper-item-box {
	width: 670rpx;
	height: 730rpx;
	background: #ffffff;
	border-radius: 20rpx;
	position: relative;
	overflow: auto;
}
.swiper-img-active {
	width: 670rpx;
  height: 912rpx;
  transition: all 0.5s;
	background: #ffffff;
	border-radius: 20rpx;
}

.from-card-title{
	min-height: 80rpx;
	background: #fff;
	position: sticky;
	top: 0;
	border-radius: 20rpx 20rpx 0 0;
	box-sizing: border-box;
}
.from-card-bg{
	position: absolute;
	top: -2rpx;
	left: 0;
	width: 100%;
	height: 100%;
	z-index:999;
}
.noData image {
  display: block;
  width: 420rpx;
  height: 312rpx;
  margin: 0 auto;
}
.form-item{
	border-radius: 8rpx;
}