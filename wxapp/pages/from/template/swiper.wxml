<template name="swiper">
	<view class="swiper-box" wx:if="{{list.length}}">
		<swiper current="{{currentNum}}" bindchange='onSwiperChange' previousMargin="27rpx" nextMargin="27rpx">
			<swiper-item wx:for="{{list}}">
				<view class="swiper-item-box {{currentNum == index ? 'swiper-img-active':''}}">
					<view class="from-card-title pt25 pb25 pl20 pr20 flex w100">
						<image class="from-card-bg" src="{{static.bg_follow_task}}" />
		
						<image style="width:46rpx;height:46rpx" mode="aspectFill" src="{{static.ic_task}}" />
						<text class="color-primary f32 b ml10 flex1">{{item.name}}</text>
					</view>
					<view class="p30">
						<view class="flex">
							<view class="mr10">
								<image style="width:40rpx;height:40rpx" mode="aspectFill" src="{{static.ic_follow_person}}" />
							</view>
							<view>
								<view class="c999 f28">
									随访人
								</view>
								<view class="c333 f28 mt20">
									{{item.inquirerName}}
								</view>
							</view>
						</view>
						<view class="flex mt30">
							<view class="mr10">
								<image style="width:40rpx;height:40rpx" mode="aspectFill" src="{{static.ic_follow_doctor}}" />
							</view>
							<view>
								<view class="c999 f28">
									随访医生
								</view>
								<view class="c333 f28 mt20">
									{{item.doctorName}}
								</view>
							</view>
						</view>
						<view class="flex mt30">
							<view class="mr10">
								<image style="width:40rpx;height:40rpx" mode="aspectFill" src="{{static.ic_follow_time}}" />
							</view>
							<view>
								<view class="c999 f28">
									随访日期
								</view>
								<view class="c333 f28 mt20">
									{{item.beginTime}} - {{item.endTime}}
								</view>
							</view>
						</view>
						<view class="flex mt30">
							<view class="mr10">
								<image style="width:40rpx;height:40rpx" mode="aspectFill" src="{{static.ic_follow_form}}" />
							</view>
							<view class="flex1 w100">
								<view class="c999 f28 flex1">
									随访表单
								</view>
								<view class="c333 f28 mt20 p20 flex_lr bg-color-gray-light form-item" bindtap='handleGoFrom' data-formId='{{i.followUpRecordFormId}}' data-id='{{item.id}}' wx:for="{{item.followUpFormList}}"  wx:for-item="i" wx:for-index="i_index">
									<view class="flex1">
										{{i.formName}}
									</view>
									<view class="flex_m f24 tr ml20 {{i.fillingStatus == 0 ? 'c999':'color-success'}}">{{i.fillingStatus == 0 ? '未填写':'已填写'}} 
									<text class="rigth-icon f10" wx:if='{{listQuery.status!=0}}'></text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>
	</view>
		<!-- 无数据 -->
	<view class="noData" style="padding-top:300rpx" wx:else>
		<view class="tc cfff f28 pt40 pb40">当前无随访任务</view>
	</view>

</template>