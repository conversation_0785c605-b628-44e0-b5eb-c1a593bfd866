// pages/from/fromDetail/index.js
const api = require('../../../config/api.js')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    url: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    const id = options.id
    const formId = options.formId
    console.log(options, 18)
    const url = api.WebViewUrl + `/#/fromList?id=${id}&formId=${formId}&origin=wx&type=1&token=` + wx.getStorageSync('token')
    // const url =`http://10.9.0.70:8066/#/fromList?id=${id}&formId=${formId}&origin=wx&type=1&token=` + wx.getStorageSync('token')
    console.log(url, 19)
    this.setData({
      url, id
    })
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function() {

  }
})
