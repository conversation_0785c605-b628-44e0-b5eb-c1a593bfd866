// pages/evalList/evalList.js
const api = require('../../config/api.js')
const util = require('../../utils/util')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    imgObject: {
      ic_star_bright: api.ImgUrl + 'images/ic_star_bright.png',
      ic_star_grey: api.ImgUrl + 'images/ic_star_grey.png',
      nomes: api.ImgUrl + 'images/nomes.png'
    },
    doctorId: null,
    eval: [],
    listQuery: {
      page: 1 // 页码
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '全部评价'
  },
  getEvalList() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    var requestData = {
      doctorId: this.data.doctorId,
      page: this.data.listQuery.page
    }
    util.request(api.evalList, requestData, 'post', 2)
      .then(res => {
        util.hideToast()
        const result = res.data.data
        if (res.data.code === 0) {
          ++this.data.listQuery.page
          this.setData({
            eval: this.data.eval.concat(result.result),
            listQuery: this.data.listQuery,
            loadComplete: !result.hasNext ? false : true
          })
        } else {
          util.showToast({
            icon: 'none',
            title: res.data.msg
          })
        }
      })
      .catch(res => {
        console.log(res)
      })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      doctorId: options.id
    })
    this.getEvalList()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {
    if (this.data.loadComplete) {
      this.getEvalList()
    }
  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
