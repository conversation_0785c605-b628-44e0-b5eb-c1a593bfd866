<navbar isBack="{{ true }}" backgroundColor="#fff" navTitle="我的问诊"></navbar>
<view class="container bg-color-gray-light">
  <view
    class="w100 fixed zx999 flex tab bg-color-white"
    style="top:{{ 44 + statusBarHeight }}px;"
  >
    <view wx:for="{{ status }}" class="flex1 flex_c_m" wx:key="index">
      <text
        class=" {{ listQuery.consultStatus == item.id ? 'cur' : '' }}"
        bindtap="handleTabClick"
        data-id="{{ item.id }}"
        >{{ item.name }}</text
      >
    </view>
  </view>
  <view class="order_list pb10" wx:if="{{list.length}}">
    <view
      class="bg-color-white m20 container-radius"
      data-orderSn="{{item.orderSn}}"
      wx:key="id"
      catchtap="handleDetail"
      wx:for="{{ list }}"
    >
      <view class="flex_lr_m p20 bb1">
        <view class="flex_m">
          <image
            wx:if="{{ item.consultType == 1 }}"
            class="w40 h40"
            src="{{ static.ic_doctor_image }}"
          />
          <image
            wx:if="{{ item.consultType == 2 }}"
            class="w40 h40"
            src="{{ static.ic_doctor_video }}"
          />
          <text
            class="f32 c333 b ml10"
            selectable="false"
            space="false"
            decode="false"
            >{{ item.consultType == 1 ? "图文咨询" : "视频咨询" }}</text
          >
        </view>
        <view class="f28">
          <view style="color: #367dff" wx:if="{{ item.consultStatus == 1 }}">
            待接诊
          </view>
          <view style="color: #ff9b3a" wx:if="{{ item.consultStatus == 2 }}">
            进行中
          </view>
          <view style="color: #38bf87" wx:if="{{ item.consultStatus == 3 }}">
            已完成
          </view>
          <view style="color: #666666" wx:if="{{ item.consultStatus == 4 }}">
            已取消
          </view>
          <view style="color: #666666" wx:if="{{ item.consultStatus == 5 }}">
            已退款
          </view>
        </view>
      </view>
      <view class="p20">
        <view class="f28 c333"> 订单号：{{ item.orderSn }} </view>
        <view class="f28 c333 mt10">
          就诊人：{{ item.inquirerName }} {{item.inquirerGender ==1 ?'男':'女'}} {{ item.inquirerAge }}
        </view>
        <view class="f28 c333 mt10">
          问诊医生：{{ item.doctorName }} {{ item.departmentName }}
        </view>
        <view class="f28 c333 mt10">
          问诊费用：<text class="color-danger b">¥{{ item.price / 100 }}</text>
        </view>
      </view>
      <view
        class="pl20 pr20 pb20 flex_c_end"
        wx:if="{{ item.consultStatus == 3 ||item.consultStatus == 4 || item.consultStatus == 5}}"
      >
        <view
          data-status="{{item.consultStatus}}"
          data-type="{{item.consultType}}"
          data-doctorId="{{item.doctorId}}"
          data-id="{{ item.id }}"
          data-roomid="{{ item.roomId }}"
          catch:tap="handleConsult"
          class="f26 color-primary pt10 pb10 pl30 pr30 container-radius"
          style="border: 1rpx solid #2893ff"
          catchtap="handleConsult"
        >
          再次咨询
        </view>
      </view>
      <view
        class="pl20 pr20 pb20 flex_c_end"
        wx:else
      >
        <view
          data-status="{{item.consultStatus}}"
          data-type="{{item.consultType}}"
          data-doctorId="{{item.doctorId}}"
          data-id="{{ item.id }}"
          data-roomid="{{ item.roomId }}"
          catch:tap="handleConsult"
          class="f26 color-primary pt10 pb10 pl30 pr30 container-radius"
          style="border: 1rpx solid #2893ff"
          catchtap="handleConsult"
        >
          继续咨询
        </view>
      </view>
    </view>
  </view>
  <view wx:else class="flex_line_c no_msg_box">
    <image class="no_msg" src="{{static.nomes}}"></image>
    <view class="f28 c666">暂无数据</view>
  </view>
</view>
