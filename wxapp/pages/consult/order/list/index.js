// pages/consult/order/index.js
const app = getApp()
var util = require('../../../../utils/util')
var api = require('../../../../config/api.js')
Page({
  /**
   * 页面的初始数据
   */
  data: {
    status: [
      {
        id: '',
        name: '全部'
      },
      {
        id: '2',
        name: '进行中'
      },
      {
        id: '3',
        name: '已完成'
      },
      {
        id: '5',
        name: '已退款'
      }
    ],
    listQuery: {
      page: 1,
      num: 10,
      consultStatus: ''
    },
    list: [],
    loadComplete: null,
    static: {
      ic_doctor_video: api.ImgUrl + 'images/ic_doctor_video.png',
      ic_doctor_image: api.ImgUrl + 'images/ic_doctor_image.png',
      nomes: api.ImgUrl + 'images/nomes.png'
    },
    statusBarHeight: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      statusBarHeight: app.globalData.statusBarHeight
    })
    this.getList()
  },
  async getList() {
    const { listQuery, list } = this.data
    util.showLoading({
      title: 'loading'
    })
    try {
      const { data } = await util.request(`${api.consultOrderList}`, { ...listQuery }, 'get')
      if (data.code !== 0) {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
        return
      }
      this.setData({
        list:
          listQuery.page > 1 ? list.concat(data.data.result) : data.data.result,
        loadComplete: data.data.hasNext
      })
      util.hideLoading()
    } catch (error) {
      throw new Error(error)
    }
  },
  handleTabClick(e) {
    this.setData({
      'listQuery.page': 1,
      'listQuery.consultStatus': e.currentTarget.dataset.id
    })
    if (wx.pageScrollTo) {
      wx.pageScrollTo({
        scrollTop: 0
      })
    }
    this.getList()
  },
  handleConsult(e) {
    const { doctorid, status, type, id, roomid } = e.currentTarget.dataset
    if (status === 3 || status === 4 || status === 5) {
      wx.navigateTo({
        url: `/pages/famousDoctorDetail/famousDoctorDetail?type=3&doctorId=${doctorid}`
      })
      return
    }
    if (type === 1) {
      wx.navigateTo({
        url: `/pages/consult/chat/chat?doctorId=${doctorid}`
      })
    } else {
      if (status === 1) {
        util.showToast({
          title: '医生未接诊',
          icon: 'none',
          duration: 3000
        })
      } else {
        wx.navigateTo({
          url: `/pages/meeting/meeting?roomID=${roomid}&videoConsultId=${id}`
        })
      }
    }
  },
  handleDetail(e) {
    const { ordersn } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/consult/order/detail/index?orderSn=${ordersn}`
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {},

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {},

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {},

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {},

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {},

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    if (this.data.loadComplete) {
      ++this.data.listQuery.page
      this.getList()
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {}
})
