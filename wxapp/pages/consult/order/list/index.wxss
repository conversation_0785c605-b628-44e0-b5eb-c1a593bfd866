/* pages/consult/order/index.wxss */
.tab{
  width: 100%;
  height: 84rpx;
  background: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
}
.tab view{
  text-align: center;
  font-size: 28rpx;
  color: #666666;
  height: 84rpx;
  line-height: 84rpx;
  /* width: 25%; */
}
.tab view text{
  display: inline-block;
  height: 100%;
  position: relative;
}
.tab view text.cur{
  color: var(--themeColor);
  font-weight: bold;
}
.tab view text.cur::after{
  position: absolute;
  content: '';
  width: 40rpx;
  height: 8rpx;
  border-radius: 2rpx;
  background: var(--themeColor);
  left: 50%;
  margin-left: -20rpx;
  bottom: 0rpx;
}
.order_list{
  padding-top: 85rpx;
}