/* pages/consult/record/index.wxss */
@import '/pages/consult/index/index.wxss';
page{
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  background: #f7f7f7;
}
.record-wrapper {
  width: 100%;
  flex:1;
  position: absolute;
  left: 0;
  top: 160rpx;
  bottom: 160rpx;
  /* display: flex; 
  flex-direction: column-reverse; */
}
.record-chatting-item {
  width: 100%;
  padding: 20rpx 20rpx;
  box-sizing: border-box;
}
.record-item-time-wrapper {
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.record-item-time {
  border-radius: 10rpx;
  padding: 4rpx 10rpx;
  background-color: #999999;
  color: #fff;
  font-size: 26rpx;
}
.record-chatting-item-img {
  width: 88rpx;
  height: 88rpx;
  border-radius: 100%;
  display: inline-block;
}
.sendtext {
  max-width: 70%;
  background: var(--themeColor);
  border-radius: 8rpx 0 8rpx 8rpx;
  /* padding: 16rpx; */
  color: #333;
  word-wrap:break-word; 
  word-break:break-all;
  overflow: hidden;
  color: #fff;
  display: flex;
  align-content: center;
}
.receivetext {
  max-width: 70%;
  border-radius: 0 8rpx 8rpx 8rpx;
  background-color: #fff;
  border: 1px solid #fff;
  padding: 20rpx;
  color: #333;
  word-wrap:break-word; 
  word-break:break-all;
  overflow: hidden;
}
.sendimage,
.receiveimage {
  max-width: 70%;
  border-radius: 10rpx;
  box-sizing: border-box;
  word-wrap: break-word;
  overflow: hidden;
  padding: 10rpx;
}
.receiveimage {
  border: 1px solid #fff;
  background-color: #fff;
}
.receiveimage image,
.sendimage image {
  display: block;
  max-width: 200rpx;
  height: 200rpx;
}

/* tip消息富文本 */
.tip-rich-text {
  background-color: #ccc;
  text-align: center;
  align-self: center;
  height: 40rpx;
  font-size: 26rpx;
  padding: 0 20rpx;
  color: #000;
  border-radius: 10rpx;
}

.self {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  color: #fff;
}
.other {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  color: #fff;
}
.left-triangle {
  position: relative;
  height: 50rpx;
  width: 34rpx;
  margin-top: 20rpx;
}
.right-triangle {
  position: relative;
  width: 88rpx;
  height: 88rpx;
	border-radius: 50%;
  overflow: hidden;
}
.right-triangle .avatar {
  width: 100%;
  height: 100%;
	border-radius: 50%;
}
.left-triangle image {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}
.video-triangle {
  height: 0px;
  width: 0px;
  border-width: 30rpx;
  border-style: solid;
  border-color: transparent transparent transparent #777;
  margin-top: 20rpx;
  margin-left: 30rpx;
}
.left-image {
  height: 0px;
  width: 0px;
  border: 20rpx solid transparent;
}
.small-map-wrapper {
  width: 200rpx;
  height: 200rpx;
  position: relative;
  padding: 10rpx;
  background-color: #80a1d5;
  border-radius: 14rpx;
}
.small-map-wrapper .small-geo-img {
  width: 100%;
  height: 100%;
  background-color: pink;
}
.small-map-wrapper .text {
  line-height: 32rpx;
  font-size: 28rpx;
  text-align: center;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  color: #fff;
  background-color: #444;
  padding: 0 10rpx;
  border-bottom-left-radius: 14rpx;
  border-bottom-right-radius: 14rpx;
}

.small-video-wrapper {
  width: 200rpx;
  height: 200rpx;
  padding: 10rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
.small-video-wrapper .video {
  max-width: 200px;
  max-height: 300px;
}

.receiveaudio {
  background-color: #fff;
  border-radius: 0 8rpx 8rpx 8rpx;
  display: flex;
  align-items: center;
  padding: 20rpx;
  min-width: 30%;
  box-sizing: border-box;
  margin-left: -2px;
}
.sendaudio {
  background-color: var(--themeColor);
  border-radius: 8rpx 0 8rpx 8rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 20rpx;
  min-width: 30%;
  box-sizing: border-box;
  margin-left: -2px;
}
.receiveaudio {
  border-color: #fff;
}
.sendaudio .image,
.receiveaudio .image {
  width: 40rpx;
  height: 40rpx;
}
.sendaudio .text,
.receiveaudio .text {
  align-self: center;
  color: #000;
  opacity: 0.5;
}
.text {
  color: #000;
  opacity: 0.5;
  padding: 0 20rpx;
  position: relative;
}
.unread:after {
  content: " ";
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #f00;
}
.chat-btn {
	width: 100%;
  box-sizing: border-box;
  text-align: center;
  position: fixed;
  bottom: 0;
  color: #fff;
  border-radius: 12rpx 12rpx 0 0;
	padding-bottom: env(safe-area-inset-bottom);
	padding-left: 30rpx;
	padding-right: 30rpx;
	background-color: #fff;
}
.chat-btn-btn {
	height: 84rpx;
	border-radius: 42rpx;
  border: 2rpx solid var(--themeColor);
}
.chat-btn-title {
  line-height: 74rpx;
  font-size: 32rpx;
  padding-top: 10rpx;
}
.chat-btn-text {
  font-size: 26rpx;
  opacity: 0.6;
}
.showToast {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 300rpx;
  height: 300rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 10rpx;
}
.showToast > image {
  width: 100rpx;
  height: 100rpx;
  display: block;
  margin: 80rpx auto 50rpx;
}
.showToast > text {
  color: #fff;
  text-align: center;
  display: block;
}
.chat-tips {
  font-size: 26rpx;
  text-align: center;
  color: #aaa;
  opacity: 0.6;
  padding: 20rpx 0;
}
.systemChat {
  color: #333;
  font-size: 32rpx;
}
.recipel {
  background-color: #fff;
  border-radius: 4rpx;
}
.recipelMess {
  width: 490rpx;
  border-radius: 8rpx;
}
.recipelMess .title {
  height: 80rpx;
  /* background: linear-gradient(180deg, rgba(74, 163, 255, 0.3) 0%, rgba(40, 147, 255, 0) 100%); */
  border-radius: 8rpx 8rpx 0 0;
  font-weight: 600;
  position: relative;
}
.bg_image{
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.recipelMess .title2 {
  height: 80rpx;
	/* background: linear-gradient(360deg, rgba(255, 181, 94, 0) 0%, #FFEEDA 100%); */
  border-radius: 8rpx 8rpx 0 0;
  font-weight: 600;
  position: relative;
}
.recipelMess .drug {
  padding-bottom: 10rpx;
}
.recipelMess .durguse {
  padding-top: 10rpx;
}
.sys-btn {
  padding: 20rpx;
}
.record-chatting-item-text {
  padding: 20rpx;
  display: flex;
  align-items: center;
}
/* 新增样式 */
.dob-box {
  width: 100%;
  padding: 10px 3%;
  position: fixed;
  top: 0;
  left: 0;
}
.doc-photo image {
  width: 42px;
  height: 42px;
  border-radius: 50%;
}
.van-count-down {
  color: #f06454;
}
.item {
  color: #f06454;
}

button::after {
  border: none;
}
.timeBox {
  display: flex;
  align-items: center;
}

.evaluate{
	padding: 30rpx 90rpx;
	border-radius: 16rpx;
}
.evaluate-title{
	height: 90rpx;
	background: linear-gradient(180deg, rgba(74, 163, 255, 0.3) 0%, rgba(40, 147, 255, 0) 100%);
  border-radius: 16rpx 16rpx 0px 0px;
}
  
  /* 新增样式 */
.dob-box{
	width: 100%;
	padding: 10px 3%;
	position: fixed;
	top:0;
	left: 0;
}
.doc-photo image{
	width: 42px;
	height: 42px;
	border-radius: 50%;
}
.van-count-down{
	color: var(--redColor);
}
.item{
	color: var(--redColor);
}
button::after{
	border: none;
}

.tabber-item{
	height: 84rpx;
	display: flex;
	justify-content: center;
	position: relative;
}
.active{
	width: 40rpx;
	height: 8rpx;
	border-radius: 2rpx;
	background: var(--themeColor);
	position: absolute;
  bottom: 0;
  left: 50%;
  margin-left: -20rpx;
}



/* 底部按钮样式 */
.fixed-button {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 180rpx;
  background: #FFFFFF;
  height: auto;
}
.fixed-button .bttom-btn {
	background: var(--themeColor);
	border-radius: var(--btnRadius);
  font-size: 32rpx;
  text-align: center;
  color: #FFFFFF;
  line-height: 90rpx;
  margin: 0 30rpx 20rpx 30rpx;
  box-sizing: border-box;
}

.tabber{  
  position: fixed;
  left: 0;
  width: 100%;
  z-index: 999;
}
.container{
 /* height: 100%; */
 /* min-height: auto; */
 box-sizing: border-box;
 overflow: hidden;
}
.chat-container{
  /* width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  z-index: 99 */
}
.timeText{
	text-align: center;
	color: #999999;
  font-size: 26rpx;
  padding: 30rpx 0;
}
.blue{
	color: var(--themeColor);
}
.mt260{
  margin-top: 320rpx;
}

.no_msg_box {
  width: 100%;
  position: absolute;
  top: 350rpx;
}