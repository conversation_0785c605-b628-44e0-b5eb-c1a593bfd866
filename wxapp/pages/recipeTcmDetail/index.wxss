/* pages/recipeDetail/recipeDetail.wxss */
page{
  background: #f8f8f8;
  color: #333;
}
.head{
  width: 100%;
}
.head image{
  position: absolute;
  width:200rpx;
  height: 200rpx;
  top: 100rpx;
  right: 32rpx;
  z-index: 99;
}
.container{
  min-height: 100%;
  padding: 0 0 160rpx 0;
  background:#F8F8F8;
}
.chapter{
  position: absolute;
  width: 200rpx;
  height: 200rpx;
  top: -22rpx;
  right: 12rpx;
}
.icon{
  display: inline-block;
  width: 36rpx;
  height: 36rpx;
  vertical-align: top;
  margin-right: 10rpx;
}
.sign{
  display: inline-block;
  vertical-align: top;
  height: 40rpx;
}
.container .info{
  padding-right: 32rpx;
  padding-left: 32rpx;
  background: #ffffff;
}
.tag{
  width: 176rpx;
  height: 72rpx;
  text-align: center;
  border: 6rpx solid #F35A5A;
  line-height: 60rpx;
  color: #E65353;
  position: absolute;
  top: 125rpx;
  right: 64rpx;
}
.buyAgain {
  opacity: .5;
}
.c38BF87{color: var(--labelColor);}
.container .p_item .p_num {
  margin: auto 0 0 0;
}

.rel-tag{
	padding: 5rpx 10rpx;
	background: #F2F6FF;
	border-radius: 8rpx;
	color: var(--themeColor);
}
.drug_list{
	display: flex;
	flex-wrap: wrap;
}
.drug_list_item{
	margin-right: 40rpx;
	margin-bottom: 5px;
}
.drug_list_item:nth-child(4n){
	margin-right: 0;
}
.drug_detail{
	 padding: 48rpx 60rpx 48rpx 60rpx;
}
.drug_encrypt{
	height: 166rpx;
	background: linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 100%);
	/* background: #F35A5A; */
	border-radius: 8rpx;
}
.confir{
	display: flex;
}