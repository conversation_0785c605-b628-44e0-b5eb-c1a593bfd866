// pages/recipeDetail/recipeDetail.js
const api = require('../../config/api')
const util = require('../../utils/util')
const app = getApp()
Page({
  /**
	 * 页面的初始数据
	 */
  data: {
    type: null,
    recommendId: null,
    patientId: null,
    detail: {},
    imgObject: {
      reviewedNo: api.ImgUrl + 'images/ic_prescription_reviewed_no.png',
      reviewedOk: api.ImgUrl + 'images/ic_prescription_reviewed_ok.png',
      reviewedNot: api.ImgUrl + 'images/ic_prescription_reviewed_wait.png',
      ic_prescription_seal: api.ImgUrl + 'logo/ic_seal.png'
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '处方详情',
    templateId: [],
    authShow: false
  },
  onClose() {
    this.authToast.setData({
      authShow: false
    })
    wx.navigateTo({
      url: '/pages/confirmTcmOrder/index?id=' + this.data.detail.recommendId
    })
  },
  //(例子：0.1 --> 0.100)
  toFixedThree(num) {
    return (Math.round(num * 1000) / 1000).toFixed(3)
  },
  async getDetail() {
    try {
      const {
        data
      } = await util.request(api.prescriptionTcmDetail, {
        patientId: this.data.patientId,
        recommendId: this.data.recommendId
      }, 'post', 2)
      if (data.code !== 0) {
        throw new Error(data.msg)
      }
      data.data.recomTime = util.parseTime(data.data.recomTime, '{y}.{m}.{d}')
      data.data.doctorAuditTime = util.parseTime(data.data.doctorAuditTime, '{y}-{m}-{d} {h}:{i}:{s}')
      data.data.pharmacistAuditTime = util.parseTime(data.data.pharmacistAuditTime, '{y}-{m}-{d} {h}:{i}:{s}')
      data.data.alPharmacistAuditTime = util.parseTime(data.data.alPharmacistAuditTime, '{y}-{m}-{d} {h}:{i}:{s}')
      data.data.doseSalePrice = this.toFixedThree(data.data.doseSalePrice / 1000)
      data.data.totalDoseSalePrice = this.toFixedThree(data.data.doseSalePrice * data.data.tcmQuantity)
      data.data.productionFee = data.data.productionFee / 100
      data.data.totalPrice = (data.data.totalPrice / 100).toFixed(2)

      this.setData({
        detail: data.data
      })
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  onAuthSub(type) {
    // type=1 首次获取 否则第二次弹窗
    var that = this
    if (this.data.templateId.includes(null)) {
      wx.navigateTo({
        url: '/pages/confirmTcmOrder/index?id=' + that.data.detail.recommendId
      })
      return false
    }
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success: (res) => {
        if (res[that.data.templateId[0]] === 'reject' && type === 1) {
          this.authToast.setData({
            authShow: true
          })
        } else {
          wx.navigateTo({
            url: '/pages/confirmTcmOrder/index?id=' + that.data.detail.recommendId
          })
        }
      },
      fail: (res) => {
        if (type === 1) {
          this.authToast.setData({
            authShow: true
          })
        } else {
          wx.navigateTo({
            url: '/pages/confirmTcmOrder/index?id=' + that.data.detail.recommendId
          })
        }

      }
    })
  },
  async buyDrug(e) {
    if (!(this.data.type == 1 || !(this.data.detail.buy == 1 || this.data.detail.expire == 1 || this.data.detail.invalid == 1))) {
      return false
    } else {
      try {
        util.showLoading({
          title: '获取地址中...',
          mask: true
        })

        const { data } = await util.request(api.getPrescriptionOrderUrl, {
          recomId: this.data.recommendId
        }, 'post', 2)

        console.log('中药处方下单接口原始响应:', data)
        console.log('格式化后的数据:', data.data)

        util.hideLoading()

        if (data.code === 0 && data.data && data.data.jumpUrl) {
          // 跳转到高济H5页面
          wx.navigateTo({
            url: `/pages/webView/index?url=${encodeURIComponent(data.data.jumpUrl)}`
          })
        } else {
          util.showToast({
            title: data.msg || '获取下单地址失败',
            icon: 'none',
            duration: 3000
          })
        }
      } catch (error) {
        util.hideLoading()
        util.showToast({
          title: error.message || '网络错误',
          icon: 'none',
          duration: 3000
        })
      }
    }
  },
  applyFor(e) {
    this.reapply(e.target.dataset.id)
  },
  async reapply(recommendId) {
    try {
      util.showLoading({
        title: '提交中',
        mask: true
      })
      const {
        data
      } = await util.request(api.prescriptionReapply, {
        recommendId
      }, 'post', 2)
      // TODO: 暂定 申请再次购买后改变状态
      this.setData({
        'detail.require': 1
      })
      util.hideLoading()
      if (data.code === 0) {
        util.showModal({
          content: '您的求药申请已发送给' + this.data.detail.doctorName + '医生，医生会第一时间为您续方，请耐心等待~',
          showCancel: false,
          confirmText: '我知道了',
          success: (result) => {}
        })
      } else if (data.code === 210) {
        util.showModal({
          content: data.msg,
          showCancel: false,
          confirmText: '确定',
          success: function(res) {}
        })
      } else {
        util.showToast({
          title: data.msg,
          icon: 'none',
          duration: 3000
        })
      }
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
  },
  // 获取用药模板
  async getTemplate() {
    var templateId = await util.getTemplate(4)
    this.setData({
      templateId: templateId
    })
  },
  /**
	 * 生命周期函数--监听页面加载
	 */

  onLoad: function(options) {
    if (!options.recomId) {
      return wx.navigateBack({
        delta: 1
      })
    }
    this.setData({
      type: options.type,
      patientId: options.patientId,
      recommendId: options.recomId

    })
    // this.getDetail()
    // this.getTemplate()
  },
  /**
	 * 生命周期函数--监听页面显示
	 */
  onShow: function() {
    var that = this
    this.authToast = this.selectComponent('#authToast')
    setTimeout(() => {
      that.getDetail()
      that.getTemplate()
    }, 100)
  },
  download() {
    app.globalData.pdfUrl = this.data.detail.pdfUrl
    wx.navigateTo({
      url: '/pages/pdf/index'
    })
  },
  touchMove() {
    util.verifyPageMessageUpdate()
  },
  /**
	 * 生命周期函数--监听页面隐藏
	 */
  onHide: function() {
    this.setData({
      authShow: false
    })
  },

  /**
	 * 生命周期函数--监听页面卸载
	 */
  onUnload: function() {
  },

  /**
	 * 页面相关事件处理函数--监听用户下拉动作
	 */
  onPullDownRefresh: function() {

  },

  /**
	 * 页面上拉触底事件的处理函数
	 */
  onReachBottom: function() {

  }

  /**
	 * 用户点击右上角分享
	 */
  // onShareAppMessage: function() {

  // }
})
