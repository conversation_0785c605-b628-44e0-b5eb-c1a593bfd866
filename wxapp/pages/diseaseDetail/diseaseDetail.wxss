/* pages/diseaseDetail/diseaseDetail.wxss */
.container{
  padding-bottom: 224rpx;
}
textarea{
  width: 100%;
  height: 200rpx;
  /* border: 2rpx solid #EEEEEE; */
  box-sizing: border-box;
  padding: 20rpx;
	border-radius: 8rpx;
  color: #333;
  font-size: 32rpx;
  background-color: #f8f8f8;
}
.drug text{
  height: 56rpx;
  box-sizing: border-box;
  border: 2rpx solid  #EEEEEE;
  border-radius: 4rpx;
  display: inline-block;
  font-size: 28rpx;
  color: #999999;
  padding: 0 16rpx;
  line-height: 52rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}
.drug text.cur{
  background: var(--themeColor);
  border-color: var(--themeColor);
  color: #fff;
}
.upload{
  float:left;
  width:140rpx;
  height:140rpx;
  margin-top: 20rpx;
}
.upload image{
  display: block;
  width:100%;
  height:100%;
}
.picList .item{
  display: block;
  float:left;
  margin-right:40rpx;
  margin-top: 20rpx;
  width: 140rpx;
  height: 140rpx;
  position: relative;
  overflow: visible;
}
.picList .item .delPic{
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: -20rpx;
  top:-20rpx;
}
.picList .item .delPic image{
  display: block;
  width: 100%;
  height: 100%;
}
.picList .item>image{
  display: block;
  width:100%;
  height:100%;
}
.picList .item:nth-child(4n){
  margin-right: 0rpx;
}
.confir button.gray{
  background: #ddd;
}
.noIcon{
  width: 44rpx;
  height: 44rpx;
  position: absolute;
  right: 0;
  top: 50%;
  margin-top: -22rpx;
}
.lh44{
  line-height: 44rpx;
}
.tag{
  background: #E7F4FF;
  border-radius: 4rpx;
  width: 76rpx;
  height:36rpx ;
  text-align: center;
  display: inline-block;
  vertical-align: middle;
  color: var(--themeColor);
}
.rd {
  margin-left: 8rpx;
  color: #ff5555;
}
.agreement .van-checkbox{
	display: -webkit-flex;
	display: flex;
	align-items: unset;
	overflow: hidden;
	-webkit-user-select: none;
	user-select: none;
	position: relative;
}
.agreement .van-checkbox .van-checkbox__icon {
	margin-top: 10rpx;
}