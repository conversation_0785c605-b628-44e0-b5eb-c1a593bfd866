// pages/diseaseDetail/diseaseDetail.js
var api = require('../../config/api.js')
var Config = require('../../config/index.js')
const util = require('../../utils/util')
const app = getApp()
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '就诊人病情',
    tag: ['上呼吸道感染', '慢性支气管炎', '急性胃炎', '肝硬化', '糖尿病', '高血压'],
    imgObject: {
      ic_upload_add: api.ImgUrl + 'images/ic_upload_add.png',
      ic_upload_delect: api.ImgUrl + 'images/ic_upload_delect.png',
      ic_more_black: api.ImgUrl + 'images/ic_more_black.png'
    },
    checked: 0,
    recordId: null, //病情ID；
    info: {
      offlineSeeDoctor: 1, //1是  0否
      offlineDiagnosis: '', //线下诊断
      dataLossTag: 0, // 资料丢失0否 1是
      offlineDiagnosisImgs: [], //图片
      inquirerId: '',
      symptom: {
        fever: null, //是否发热
        pregnant: null, //是否为孕妇
        hypertension: null, //是否患有高血压
        diabetes: null //是否患有糖尿病
      }
    },
    peopleInfo: {}, //所有就诊人ID
    templateId: [],
    clickFlag: true,
    uploadSuccess: true,
    company: Config.company,
    hainanConfig: null,
    isAgreement: false,
    showPopup: false,
    source: null
  },
  async getTemplate() {
    var templateId = await util.getTemplate(2)
    console.log(templateId, 39)
    this.setData({
      templateId: templateId
    })
  },

  radioChange(e) {
    this.setData({
      ['info.offlineSeeDoctor']: e.detail.value * 1
    })
  },
  textChange(e) {
    this.setData({
      ['info.offlineDiagnosis']: e.detail.value ? util.filterEmoji(e.detail.value) : e.detail.value
    })
  },
  tagChoose(e) {
    var value = e.currentTarget.dataset.value
    var text = this.data.info.offlineDiagnosis
    this.setData({
      ['info.offlineDiagnosis']: text == '' ? value : text + ',' + value
    })
  },
  onChecked(e) {
    if (this.data.isAgreement) {
      this.setData({
        checked: e.detail
      })
    } else {
      // this.ageePopup.getArticle(6)
      this.ageePopup.setData({
        showPopup: true
      })
      this.setData({ showPopup: true })
    }
    console.log(this.ageePopup.data.showPopup, e, 'this.ageePopup.data.showPopup')
  },
  // 用户同意协议
  onAgreement() {
    this.setData({
      checked: true,
      isAgreement: true,
      showPopup: false
    })
    console.log('同意了')
  },
  onclosePopup() {
    const { isAgreement } = this.data
    this.setData({
      showPopup: false,
      checked: isAgreement ? true : false
    })
  },
  // 接受子组件图片数据
  imgArry(event) {
    this.setData({
      ['info.offlineDiagnosisImgs']: event.detail.srcArr
    })
  },
  // 添加就诊人病情
  async diseaseAdd() {
    var that = this
    util.showLoading({
      title: '提交中~',
      mask: true
    })
    const { hainanConfig, info } = that.data
    if (!hainanConfig.config.collectSymptom) {
      delete info.symptom
    }
    try {
      const {
        data
      } = await util.request(api.diseaseAdd, that.data.info, 'post', 1)
      if (data.code === 0) {
        that.setData({
          recordId: data.data
        })
        return true
      } else {
        util.showToast({
          title: data.msg
        })
        return false
      }
    } catch (error) {
      util.showToast({
        'title': '网络错误'
      })
      throw new Error(error.errMsg)
    }
  },
  async disclaimer() {
    var that = this
    var parmars = {
      doctorId: that.data.doctorId,
      inquirerId: that.data.info.inquirerId,
      type: 2,
      agree: true,
      recordId: that.data.recordId,
      consultType: that.data.type, //图文 or 视频
      source: that.data.source // 分享来源
    }
    try {
      const {
        data
      } = await util.request(api.disclaimer, parmars, 'post', 2)
      util.hideLoading()
      if (data.code === 0) {
        return data.data
      } else if (data.code === 210) {
        wx.showModal({
          content: data.msg,
          showCancel: false,
          confirmText: '确定',
          success: function(res) {}
        })
      } else {
        util.showToast({
          title: data.msg
        })
      }
    } catch (error) {
      util.showToast({
        'title': '网络错误'
      })
      throw new Error(error.errMsg)
    }
  },
  // 判断是否需要支付
  async pay() {
    var addState = await this.diseaseAdd()
    if (!addState) {
      return false
    }
    var flag = await this.disclaimer()
    if (!flag && flag !== 0) {
      return
    }
    if (flag != 0) {
      console.log('去支付')
      app.globalData.info = this.data.info
      this.requestSubscribeMessage(1)
    } else {
      console.log('免费')
      if (this.data.type == 1) {
        this.requestSubscribeMessage(2)
      } else {
        // 执行视频提交
        this.payinfo()
      }
    }
  },
  requestSubscribeMessage(type) {
    var that = this
    // 推送模板
    wx.requestSubscribeMessage({
      tmplIds: that.data.templateId,
      success(res) {
        console.log('success', res)
        that.goPage(type)
      },
      fail(res) {
        console.log('fail', res)
        that.goPage(type)
      }
    })
  },
  goPage(type) {
    const that = this
    if (type == 1) {
      // 跳转支付页面
      wx.navigateTo({
        url: '/pages/pay/pay?doctorId=' + that.data.doctorId + '&type=' + that.data.type + '&recordId=' + that.data.recordId + '&inquirerId=' + that.data.info.inquirerId
      })
    } else if (type == 2) {
      wx.redirectTo({
        url: '/pages/consult/chat/chat?doctorId=' + that.data.doctorId + '&pageNum=3'
      })
    } else {
      // wx.navigateTo({
      //   url: '/pages/consult/record/index?doctorId=' + that.data.doctorId + '&type=2'
      // })
      app.globalData.consultType = 2
      wx.reLaunch({
        url: '/pages/consult/index/index?doctorName'
      })

    }
  },
  payinfo() {
    util.showLoading({
      title: '请稍后',
      mask: true
    })
    var that = this
    var data = {
      doctorId: that.data.doctorId,
      price: 0,
      recordId: that.data.recordId,
      inquirerId: that.data.info.inquirerId,
      offlineDiagnosisImgs: that.data.info.offlineDiagnosisImgs,
      offlineDiagnosis: that.data.info.offlineDiagnosis,
      conditionDesc: that.data.info.description
    }
    util.request(api.videoPayInfo, data, 'post', 1)
      .then(res => {
        util.hideLoading()
        if (res.data.code == 0) {
          this.requestSubscribeMessage(3)
        } else {
          util.showToast({
            title: res.data.msg,
            icon: 'none'
          })
        }
      })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  async onLoad(options) {
    this.ageePopup = this.selectComponent('#ageePopup') //his就诊记录弹窗
    const hainanConfig = await util.getHaiNanConfig()
    this.setData({
      type: options.type,
      doctorId: options.doctorId,
      source: options.source || null,
      ['info.inquirerId']: options.inquirerId,
      ['info.doctorId']: options.doctorId,
      hainanConfig
    })
    // await this.getTemplate()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: async function() {
    var id = this.data.info.inquirerId
    var list = await util.getPeopleList()
    this.setData({
      peopleInfo: {},
      ['info.inquirerId']: ''
    })
    if (id) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].inquirerId === id) {
          list[i].phone = util.stringHidden(list[i].phone, 3, 4)
          list[i].guardianPhone = util.stringHidden(list[i].guardianPhone, 3, 4)
          this.setData({
            peopleInfo: list[i],
            ['info.inquirerId']: id
          })
          break
        }
      }
    } else {
      for (let i = 0; i < list.length; i++) {
        if (list[i].idCard && list[i].relation === 0) {
          list[i].phone = util.stringHidden(list[i].phone, 3, 4)
          this.setData({
            peopleInfo: list[i],
            ['info.inquirerId']: list[i].inquirerId
          })
          break
        }
      }
    }
    await this.getTemplate()
    await this.ageePopup.getArticle()
  },
  onInputVal(e) {
    const val = e.detail.value
    const {
      key
    } = e.currentTarget.dataset
    this.setData({
      [`info.${key}`]: util.filterEmoji(val) || ''
    })
    console.log(this.data.info, 'this.data.info')
  },
  // 高热、孕妇、高血压、糖尿病勾选
  handleChange(e) {
    const {
      type
    } = e.currentTarget.dataset
    const {
      value
    } = e.detail
    this.setData({
      [`info.symptom.${type}`]: value
    })
    console.log(this.data.info, 303)
  },
  handleShowAgm() {
    this.setData({ showPopup: true })
    this.ageePopup.setData({
      showPopup: true
    })
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function() {

  // }
})
