<view class="container pb20 bg-color-gray-light" style="padding-bottom:160rpx" bindtouchmove="touchMove">
	<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
	<view class="bg-color-white p30 rel f28 c333 mt20">
		<view class="pb20">姓名：{{result.name}}</view>
		<view class="pb20">性别：{{result.gender==1?'男':result.gender==0?'女':''}}</view>
		<view class="pb20">年龄：{{result.age}}</view>
		<view class="pb20">科室：{{result.departmentName}}</view>
		<view>时间：{{result.finishTimeDate}}</view>
		<view class="chapter">
			<image src="{{ic_prescription_seal}}" class="imgBlock"></image>
		</view>
	</view>
	<view class="bg-color-white p30 mt20" wx:if="{{result.doctorMedicalRecord.diagnosisList}}">
		<view class="bb1" wx:if='{{settings && settings[7].enabled || !settings}}'>
			<view class="f32 b c333">诊断</view>
			<view class="pt20 pb30 c333 f28">
      <text wx:for='{{result.doctorMedicalRecord.diagnosisList}}'>{{index!==0 ? ',':''}}{{item}}</text>
      </view>
		</view>
		<view class="pt30" wx:if='{{settings && settings[8].enabled || !settings}}'>
			<view class="f32 b c333">治疗意见</view>
			<view class="pt20 pb30 c333 f28">
				{{ result.doctorMedicalRecord.treatmentOptions?result.doctorMedicalRecord.treatmentOptions:'无' }}</view>
		</view>
	</view>
	<view class="bg-color-white p30 mt20">
		<view class="bb1" wx:if='{{settings && settings[0].enabled ||!settings}}'>
			<view class="f32 b c333">主诉</view>
			<view class="pt20 pb30 c333 f28">{{ result.doctorMedicalRecord.mainComplaint }}</view>
		</view>
		<view class="pt30 bb1" wx:if='{{settings && settings[9].enabled ||!settings}}'>
			<view class="f32 b c333">个人史</view>
			<view class="pt20 pb30 c333 f28"> {{ result.doctorMedicalRecord.personalHistory ? result.doctorMedicalRecord.personalHistory : '无' }}</view>
		</view>
		<view class="pt30 bb1" wx:if='{{settings && settings[1].enabled ||!settings}}'>
			<view class="f32 b c333">现病史</view>
			<view class="pt20 pb30 c333 f28"> {{ result.doctorMedicalRecord.presentDisease }}</view>
		</view>
		<view class="pt30 bb1" wx:if='{{settings && settings[2].enabled ||!settings}}'>
			<view class="f32 b c333">既往史</view>
			<view class="pt20 pb30 c333 f28"> {{ result.doctorMedicalRecord.pastHistory }}</view>
		</view>
		<view class="pt30 bb1" wx:if='{{settings && settings[3].enabled ||!settings}}'>
			<view class="f32 b c333">过敏史</view>
			<view class="pt20 pb30 c333 f28"> {{ result.doctorMedicalRecord.allergy }}</view>
		</view>
		<view class="pt30 bb1" wx:if='{{settings && settings[4].enabled ||!settings}}'>
			<view class="f32 b c333">家族史</view>
			<view class="pt20 pb30 c333 f28">{{ result.doctorMedicalRecord.pastFamily }}</view>
		</view>
		<view class="pt30 bb1"
			wx:if="{{result.gender==0 && settings &&  settings[5].enabled || result.gender==0 &&!settings}}">
			<view class="f32 b c333">月经史</view>
			<view class="pt20 pb30 c333 f28">
				<view>
					月经情况：{{ !result.doctorMedicalRecord.menstrual.status && result.doctorMedicalRecord.menstrual.status !==0  ? '未填写' : menstrualStatus[result.doctorMedicalRecord.menstrual.status]}}
				</view>
				<view wx:if="{{result.doctorMedicalRecord.menstrual.status==1}}" hover-class="none"
					hover-stop-propagation="false">
					<view wx:if='{{result.doctorMedicalRecord.menstrual.firstAge}}'>
						初潮年龄：{{ result.doctorMedicalRecord.menstrual.firstAge + '岁' }}</view>
					<view wx:else>初潮年龄：无</view>
					<view wx:if='{{ result.doctorMedicalRecord.menstrual.cycle}}'>
						月经周期：{{ result.doctorMedicalRecord.menstrual.cycle?(result.doctorMedicalRecord.menstrual.cycle + '天'):'' }}
					</view>
					<view wx:else>月经周期：无</view>
					<view wx:if='{{result.doctorMedicalRecord.menstrual.processDays}}'>
						行经天数：{{ result.doctorMedicalRecord.menstrual.processDays?(result.doctorMedicalRecord.menstrual.processDays + '天'):'' }}
					</view>
					<view wx:else>行经天数：无</view>
					<view>是否痛经：{{ result.doctorMedicalRecord.menstrual.dysmenorrhea?'是':'否' }}</view>
					<view wx:if='{{result.doctorMedicalRecord.menstrual.part}}'>
						痛经描述：{{ result.doctorMedicalRecord.menstrual.part }}</view>
				</view>
			</view>
		</view>
		<view class="pt30" wx:if='{{settings && settings[6].enabled ||!settings}}'>
			<view class="f32 b c333">辅助检查</view>
			<view class="pt20  c333 f28">
				<!-- <view>体温：{{ result.doctorMedicalRecord.temperature ? result.doctorMedicalRecord.temperature + '度' : '无'}}
				</view>
				<view>体重：{{ result.doctorMedicalRecord.weight ? result.doctorMedicalRecord.weight + 'kg' : '无'}}</view>
				<view>心率：{{ result.doctorMedicalRecord.heartRete ? result.doctorMedicalRecord.heartRete + 'bpm' : '无'}} </view>
				<view>收缩压：{{ result.doctorMedicalRecord.systolic ? result.doctorMedicalRecord.systolic + 'mmHg' : '无'}}</view>
				<view>舒张压：{{ result.doctorMedicalRecord.diastole ? result.doctorMedicalRecord.diastole + 'mmHg' : '无'}} </view>
				<view>阳性体征：{{ result.doctorMedicalRecord.positiveSigns?result.doctorMedicalRecord.positiveSigns:'无' }} </view>
				<view>阴性体征：{{ result.doctorMedicalRecord.negativeSigns?result.doctorMedicalRecord.negativeSigns:'无' }} </view>
				<view>更多检查结果：{{ result.doctorMedicalRecord.moreExamine ? result.doctorMedicalRecord.moreExamine :'无'}}</view> -->
				<view>{{ result.doctorMedicalRecord.moreExamine ? result.doctorMedicalRecord.moreExamine :'无'}}</view>
			</view>
		</view>
	</view>
	<view class="bg-color-white p30 mt20 clearfix pb5" wx:if="{{result.doctorMedicalRecord.imgList.length>0}}">
		<view class="imgItem fl" wx:for="{{result.doctorMedicalRecord.imgList}}" wx:key="index">
			<image src="{{item.imgUrl}}" class="imgBlock" data-index="{{index}}" bindtap="previewImg"></image>
		</view>
	</view>
	<view class="bg-color-white p30 mt20 f28 c333">
		<view>
			医生：{{result.signInfo.doctorName }}
			<image wx:if="{{result.signInfo.sealImage}}" src="{{result.signInfo.sealImage}}" mode="aspectFit" class="sign">
			</image>
			<text class="f24 c666">{{ result.signInfo.signTime }}</text>
		</view>
		<view class="pt20">医院：{{ result.hospitalName }}</view>
	</view>
	<view class="fixed b0 l0 w100 bg-color-white">
		<view class="confir pl30 pr30">
			<button bind:tap="download">电子病历pdf</button>
		</view>
	</view>

</view>