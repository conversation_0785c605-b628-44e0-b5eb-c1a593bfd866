var api = require('../../config/api.js')
var util = require('../../utils/util')
var app = getApp()

// pages/caseDetail/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    ...app.globalData.userInfo,
    id: null,
    result: {},
    ic_prescription_seal: api.ImgUrl + 'logo/ic_seal.png',
    navTitle: '病历详情',
    isBack: true,
    backgroundColor: '#fff',
    menstrualStatus: ['未初潮', '已初潮', '已绝经'],
    settings: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    this.setData({
      id: options.id
    }, () => {
      this.getDetail()
    })
  },
  getDetail() {
    util.showToast({
      title: '加载中..',
      icon: 'loading'
    })
    util.request(api.caseDetail, {
      caseId: this.data.id,
      patientId: this.data.userId
    }, 'POST', 2)
      .then(res => {
        if (res.data.code === 0) {
          this.setData({
            result: res.data.data,
            settings: res.data.data.medicalRecordSettingList
          })
          console.log(this.data.settings, 48)
          util.hideToast()
        } else {
          util.showToast({
            title: res.data.msg
          })
        }

      })
  },
  previewImg: function(e) {
    var index = e.currentTarget.dataset.index
    var imgArr = this.data.result.doctorMedicalRecord.imgList
    var imgList = []
    imgArr.forEach(element => {
      imgList.push(element.imgUrl)
    })
    wx.previewImage({
      current: imgList[index], //当前图片地址
      urls: imgList, //所有要预览的图片的地址集合 数组形式
      success: function(res) { },
      fail: function(res) { },
      complete: function(res) { }
    })
  },
  download() {
    app.globalData.pdfUrl = this.data.result.pdfUrl
    console.log(this.data.result.pdfUrl,75)
    wx.navigateTo({
      url: '/pages/pdf/index'
    })
  },
  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },
  touchMove() {
    util.verifyPageMessageUpdate()
  },
  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function() {

  // }
})
