// pages/recipeInfo/recipeInfo.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '处方信息',
    radioId: [1, 1, 1],
    infoList: [
      {
        title: '1.您是否使用过所选药品且无相关禁忌症？',
        label: [
          { id: 1, name: '是', checked: 'true' },
          { id: 2, name: '否' }
        ]
      },
      {
        title: '2.您是否对该药物过敏？',
        label: [
          { id: 1, name: '是', checked: 'true' },
          { id: 2, name: '否' }
        ]
      },
      {
        title: '3.有无不良反应？',
        label: [
          { id: 1, name: '是', checked: 'true' },
          { id: 2, name: '否' }
        ]
      }

    ]
  },
  radioChange: function(e) {
    var index = e.target.dataset.index
    var value = e.detail.value
    var arry = 'radioId[' + index + ']'
    this.setData({
      [arry]: value
    })
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
