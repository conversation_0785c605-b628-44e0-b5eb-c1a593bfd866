<!--pages/recipeInfo/recipeInfo.wxml-->
<view class="container" style="padding-bottom:128rpx;">
  <navbar  isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
  <view class="tc w100 pt25 pb25 heading">请填写以下信息以便于医生为您诊断开方
  </view>
  <view class="bg-color-white w100 p30">
    <view class="f32 b c333">用药人信息<text class="b rd f32">*</text></view>
    <view class="clearfix mt20 rel">
      <navigator open-type='navigate' url='../people/people'>
        <view class="c333 f36">余大力 <text class="label_1 f24">已实名</text> <text class="label_2 f24">本人</text></view>
        <view class="phone f28 c333">158****0904</view>
        <view class="more"></view>
      </navigator>
    </view>
  </view>
  <view class="bg-color-white w100 p30  mt20">
    <view class="f32 b c333">请选择线下已确认疾病<text class="b rd f32">*</text></view>
    <view class="disease">
      <text class="cur">慢性胃炎及溃疡</text>
      <text>肺肾两虚证</text>
      <text>慢性支气管炎</text>
      <text>浅表性胃炎</text>
      <text>返流性食管炎</text>
    </view>
  </view>
  <view class="bg-color-white w100 p30 mt20 pb10">
    <view class="f32 b c333 pb30">请确认以下信息<text class="b rd f32">*</text></view>
    <view wx:for="{{infoList}}" wx:key="index">
      <view class="f28 c333">{{item.title}}</view>
      <radio-group class="radio" bindchange="radioChange" data-index="{{index}}">
        <view class="dib mr30 chooseItem" wx:for="{{item.label}}">
          <radio value="{{item.id}}" checked="{{item.checked}}">
            <text class="f28 ml10">{{item.name}}</text>
          </radio>
        </view>
      </radio-group>
    </view>


  </view>
  <view class="bg-color-white w100 p30 mt20">
    <view class="f32 b c333 ">上传病历资料(选填)</view>
    <view class="f28 c999 pt10">请补充线下就诊的历史处方/病历/住院出院
      <!-- 缺少设计图 -->
    </view>
  </view>
  <view class="fixed b0 l0 w100 bg-color-white pl30 pr30 confir" >
    <button>免费问诊并开处方</button>
  </view>
</view>