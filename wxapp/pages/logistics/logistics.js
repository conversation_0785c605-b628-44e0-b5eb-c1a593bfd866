// pages/logistics/logistics.js
const util = require('../../utils/util')
const api = require('../../config/api')
Page({

  /**
   * 页面的初始数据
   */
  data: {
    isBack: true,
    backgroundColor: '#fff',
    activeColor: util.THEMECOLOR,
    navTitle: '物流详情',
    steps: [],
    info: {},
    static: {
      ic_logistics_hook: api.ImgUrl + 'images/ic_logistics_hook.png'
    }
  },
  async getshipping(orderId) {
    util.showLoading({
      title: '请稍后',
      mask: true
    })
    try {
      const { data } = await util.request(util.getRealUrl(api.orderShipping, orderId))
      if (data.code !== 0) {
        throw new Error(data.msg)
      } else {
        const arry = []
        data.data.traces.forEach(element => {
          arry.push({ text: element.opeTime, desc: element.opeRemark })
        })
        this.setData({
          info: data.data,
          steps: arry
        })
      }
    } catch (error) {
      util.showToast({
        title: error.message,
        icon: 'none',
        duration: 3000
      })
    }
    util.hideLoading()
  },
  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log(options)
    this.getshipping(options.orderId)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function() {

  }

  /**
   * 用户点击右上角分享
   */
  // onShareAppMessage: function () {

  // }
})
