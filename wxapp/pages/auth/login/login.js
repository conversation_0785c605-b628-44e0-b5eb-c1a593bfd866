var api = require('../../../config/api.js')
var util = require('../../../utils/util.js')
var Config = require('../../../config/index.js')
const user = require('../../../utils/user.js')
import Toast from '../../../lib/vant-weapp/toast/toast'
var app = getApp()
Page({
  data: {
    userInfo: {},
    canIUseGetUserProfile: false,
    company: Config.company,
    static: {
      login_logo: api.ImgUrl + 'logo/ic_company.png'
    },
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '登录',
    paramsObj: null,
    checked: false,
    hasUserInfo: false,
    phone: '',
    userAvatar: '',
    nickName: '',
    show: false
  },
  onLoad: function(options) {
    // 页面初始化 options为页面跳转所带来的参数
    console.log(options, 'options')
    const params = options && options.params
    // 解码并反序列化 JSON 字符串为对象
    if (params) {
      const paramsObj = JSON.parse(decodeURIComponent(params))
      this.setData({ paramsObj })
    }
    if (wx.getUserProfile) {
      this.setData({
        canIUseGetUserProfile: true
      })
    }
  },

  oneClick() {
    if (!this.data.checked) {
      util.showToast({ title: '请先勾选协议', icon: 'none', duration: 1500 })
      return
    }
    this.setData({
      checked: true
    })
  },
  // 协议勾选
  onChange(event) {
    this.setData({
      checked: event.detail ? 1 : 0,
      hasUserInfo: event.detail ? true : false
    })
    if (this.data.hasUserInfo) {
      this.getUserProfile()
    }
  },

  // 手机号组件回调事件
  getPhoneNumber(e) {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo) {
        user.loginByWeixin().then(response => {
          user.setUserInfo(response.data.data.userInfo)
          if (response.data.code == 0 && response.data.data.loginStatus == 1) {
            // loginStatus=1未注册
            setTimeout(() => {
              this.startRegister(util.getUserInfo(), () => {
                // 注册成功后再获取手机号
                this.fetchPhone(e, response.data.data.userInfo.openId)
              })
            }, 500)
          } else {
            // loginStatus=0登录成功
            this.fetchPhone(e, response.data.data.userInfo.openId)
            util.showToast({ title: '登录成功' })
            app.globalData.hasLogin = true
            this.goBack()
          }
        })
      } else {
        Toast({ message: '用户已登录', position: 'bottom', duration: 2000 })
        // 如果用户已登录，直接跳转到首页
        wx.navigateTo({
          url: '/pages/home/<USER>'
        })
      }
    } else {
      Toast({ message: '用户拒绝授权', position: 'bottom', duration: 2000 })
    }
  },

  // 获取手机号
  fetchPhone(e, openId) {
    util.request(util.getRealUrl(api.AuthGetPhone, e.detail.code), {}, 'get')
      .then(res => {
        if (res.data.code === 0) {
          const { data } = res.data
          const userInfo = wx.getStorageSync('userInfo')
          userInfo.phone = data.phoneNumber
          util.setUserInfo(userInfo)
          this.setData({
            phone: data.phoneNumber
          }, () => {
          // 获取到手机号后，调用上传用户信息接口
            util.request(api.uploadUserInfo, { phone: data.phoneNumber }, 'post', 1, false)
              .then(uploadRes => {
                if (uploadRes.data.code !== 0) {
                  util.showToast({ title: uploadRes.data.msg, icon: 'none' })
                }
              }).catch(err => {
                util.showToast({ title: '上传用户信息失败', icon: 'none' })
              })
          })
        } else {
          Toast({ message: res.data.msg, position: 'bottom', duration: 2000 })
        }
      })
  },

  // 保存成功回到上一页
  goBack() {
    const that = this
    const pages = getCurrentPages()
    const prevPage = pages[pages.length - 2] // 获取上一个页面实例对象
    const options = wx.getStorageSync('e') ? wx.getStorageSync('e') : null
    const orderId = wx.getStorageSync('orderId') ? wx.getStorageSync('orderId') : ''

    console.log('登录成功，准备返回上一页:', {
      hasPrevPage: !!prevPage,
      prevPageRoute: prevPage ? prevPage.route : null,
      hasParamsObj: !!that.data.paramsObj,
      paramsObj: that.data.paramsObj
    })

    // 检查是否有传递的参数对象（用于页面重新加载）
    if (that.data.paramsObj) {
      console.log('检测到页面参数，准备重新跳转到目标页面')
      that.redirectToTargetPage()
      return
    }

    if (prevPage) {
      setTimeout(() => {
        wx.navigateBack({
          delta: 1,
          success: function() {
            if (options) {
              prevPage.setData({
                counselorId: options.id,
                patientId: app.globalData.userInfo.userId,
                avatar: app.globalData.userInfo.avatar//用户头像
              })
              prevPage.initCounselor(options)
            }
            // 返回代付详情
            if (orderId) {
              prevPage.setData({
                orderId: orderId
              })
              prevPage.getDetail(orderId)
            }
            // 刷新上一个页面数据（原有逻辑保持不变）
            if (that.data.paramsObj && prevPage && prevPage.onLoad) {
              prevPage.onLoad(that.data.paramsObj)
            }
          }
        })
      }, 500)
    } else {
      // 如果没有上一页，跳转到首页
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  /**
   * 重新跳转到目标页面（用于登录后恢复页面状态）
   */
  redirectToTargetPage() {
    const paramsObj = this.data.paramsObj
    if (!paramsObj) {
      console.log('没有目标页面参数，跳转到首页')
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
      return
    }

    try {
      // 构建目标页面URL
      let targetUrl = '/pages_videoConsult/consultRoom/index'
      const params = []

      // 添加所有参数到URL
      Object.keys(paramsObj).forEach(key => {
        if (paramsObj[key] !== undefined && paramsObj[key] !== null) {
          params.push(`${key}=${encodeURIComponent(paramsObj[key])}`)
        }
      })

      if (params.length > 0) {
        targetUrl += '?' + params.join('&')
      }

      console.log('登录成功，重新跳转到视频咨询房间:', {
        targetUrl,
        originalParams: paramsObj
      })

      wx.redirectTo({
        url: targetUrl,
        success: () => {
          console.log('成功跳转到视频咨询房间页面')
        },
        fail: (err) => {
          console.error('跳转到视频咨询房间页面失败:', err)
          // 如果跳转失败，尝试使用 navigateTo
          wx.navigateTo({
            url: targetUrl,
            fail: (err2) => {
              console.error('navigateTo 也失败，跳转到首页:', err2)
              wx.switchTab({
                url: '/pages/home/<USER>'
              })
            }
          })
        }
      })

    } catch (err) {
      console.error('构建目标页面URL失败:', err)
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  goAgreement(e) {
    const { type } = e.currentTarget.dataset
    wx.navigateTo({
      url: '/pages/agreement/index?type=' + type
    })
  },

  onReady: function() {

  },
  onShow: function() {

  },
  onHide: function() {
    // 页面隐藏

  },
  onUnload: function() {
    // 页面关闭

  },
  getUserProfile(e) {
    if (!this.data.checked) {
      Toast({ message: '请先同意协议后再登录', position: 'bottom', duration: 2000 })
      return
    }
    // 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
    // 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
    wx.getUserProfile({
      desc: '授权微信信息', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
      success: (res) => {
        console.log(res, 35)
        this.setData({
          userInfo: res,
          hasUserInfo: true
        })
      }
    })
  },
  wxLogin: function(e) {
    if (!this.data.checked) {
      util.showToast({ title: '请先勾选协议', icon: 'none', duration: 1500 })
      return
    }

    if (e.detail.userInfo == undefined) {
      app.globalData.hasLogin = false
      util.showToast({ title: '登录失败' })
      return
    }
    this.setData({
      userInfo: e.detail
    }, () => {
      // this.checkLoginFunc()
    })
  },
  checkLoginFunc() {
    util.checkLogin().then(() => {
      this.startRegister(util.getUserInfo())
    }).catch(() => {
      util.loginByWeixin().then(res => {
        if (res.data.code == 0 && res.data.data.loginStatus == 1) {
          // loginStatus=1未注册
          this.startRegister(util.getUserInfo())
        } else {
          util.showToast({ title: '登录成功' })
          app.globalData.hasLogin = true
          this.getUserInfo()
          wx.navigateBack({
            delta: 1
          })
        }
      }).catch((err) => {
        console.log(err, '============')
        app.globalData.hasLogin = false
        util.showToast({ title: '登录失败' })
      })

    })
  },
  startRegister: function(userInfo, callback) {
    // 允许授权获取用户信息
    this.data.userInfo.openId = userInfo.openId
    this.data.userInfo.unionId = userInfo.unionId
    this.data.userInfo.phone = userInfo.phone
    this.requestRegister(this.data.userInfo, callback)
  },
  requestRegister: function(params, callback) {
    util.registerUser(params).then(res => {
      if (res.data.code == 0) {
        util.showToast({ title: '登录成功' })
        if (callback && typeof callback === 'function') {
          callback()
        }
        this.goBack()
      } else {
        util.showModal({
          title: '错误信息',
          content: res.msg,
          showCancel: false
        })
      }
    }).catch((err) => {
      app.globalData.hasLogin = false
      util.showToast({ title: '登录失败' })
    })
  },
  accountLogin: function() {
    wx.navigateTo({
      url: '/pages/auth/register/register'
    })
  },
  getUserInfo() {
    util.request(api.userInfo).then(res => {
      if (res.data.code == 0) {
        console.log(126, res.data.data)
        const result = res.data.data
        util.setUserInfo(res.data.data)
        wx.setStorageSync('baseInfo', res.data.data)
        console.log('login.js -> getUserInfo', res.data.data)
      } else {
        util.showToast({ title: res.msg })
      }
    }).catch((err) => {
      console.log(err)
    })
  },

  noLogin() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  }
})
