<navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
<view class="login flex_line_c">
  <image class="logo" mode="widthFix" src="{{static.login_logo}}" />
  <view class="f28 c666">{{company}}</view>
  <view class="login-box">
    <block wx:if="{{!hasUserInfo}}">
      <button wx:if="{{canIUseGetUserProfile}}" class="wx-login-btn flex_c_m f32 tc themeBtn f-b" bindtap="getUserProfile">一键授权手机号登录</button>
      <button wx:else open-type="getUserInfo" class="wx-login-btn flex_c_m f32 tc themeBtn f-b" bindgetuserinfo="wxLogin">一键授权手机号登录</button>
    </block>
    <block wx:else>
      <button open-type="getPhoneNumber" bindgetphonenumber="getPhoneNumber" class="wx-login-btn flex_c_m f32 tc themeBtn f-b">一键授权手机号登录</button>
    </block>
    <view class="no-login" bindtap="noLogin">暂不登录</view>
    <view class="w100 flex agreement">
      <van-checkbox value="{{ checked }}" bind:change="onChange" data-type="checked" icon-size="28rpx" custom-class="dib"><text class="c666 f24">我已阅读并同意</text><text class="f24 color-primary" data-type='11' catchtap='goAgreement'>《用户协议》</text><text class="f24 color-primary" data-type='12' catchtap='goAgreement'>《隐私政策》</text>
      </van-checkbox>
    </view>
  </view>
  <van-toast id="van-toast" />
</view>

