.logo {
  margin-top: 200rpx;
  width: 288rpx;
  height: 288rpx;
}

.wx {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
}

.mt28 {
  margin-top: 28rpx;
}

.login-box {
  width: 630rpx;
  height: 96rpx;
  margin-top: 176rpx;
  /* position: absolute;
  bottom: 584rpx; */
}

.agreement {
  justify-content: center;
  margin-top: 266rpx;
}

.no-login {
  font-family: PingFang-SC, PingFang-SC;
  font-size: 32rpx;
  color: #999999;
  text-align: center;
  margin-top: 32rpx;
}

.f-b {
  font-weight: bold;
}

.themeBtn {
  border-radius: 48rpx;
}

.content {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.title {
  font-weight: 600;
  font-size: 32rpx;
  color: #333333;
  text-align: center;
  margin-top: 60rpx;
}

.photo {
  background-color: #fff;
  overflow: hidden;
  height: 160rpx;
  margin-top: 100rpx;
  margin-bottom: 60rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
}

.nick {
  background: #ffffff;
  border-radius: 48rpx;
  border: 2rpx solid #dddddd;
  margin: 0 60rpx;
  height: 96rpx;
}

.nick input {
  width: 100%;
  height: 100%;
  text-align: left;
  padding-left: 60rpx;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
}

.btn {
  height: 96rpx;
  background: #367dff;
  border-radius: 48rpx;
  margin: 100rpx 60rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.confirm {
  font-weight: bold !important;
  font-size: 32rpx !important;
  color: #ffffff !important;
  border: none !important;
  width: 100%;
}
