module.exports = {
  // 字符串打码
  stringHidden(str, frontLen, endLen) {
    if (str === '' || str === null || str === 'null') {
      return ''
    }
    var len = str.length - frontLen - endLen
    var xing = ''
    for (var i = 0; i < len; i++) {
      xing += '*'
    }
    return str.substring(0, frontLen) + xing + str.substring(str.length - endLen)
  },
  //根据身份证号识别出生日期 性别 年龄
  IdCard(UUserCard, num) {
    if (num == 1) {
      //获取出生日期
      var birth = UUserCard.substring(6, 10) + '-' + UUserCard.substring(10, 12) + '-' + UUserCard.substring(12, 14)
      return birth
    }
    if (num == 2) {
      //获取性别
      if (parseInt(UUserCard.substr(16, 1)) % 2 == 1) {
        //男
        return 1
      } else {
        //女
        return 0
      }
    }
    if (num == 3) {
      //获取年龄
      var myDate = new Date()
      var month = myDate.getMonth() + 1
      var day = myDate.getDate()
      var age = myDate.getFullYear() - UUserCard.substring(6, 10) - 1
      if (UUserCard.substring(10, 12) < month || UUserCard.substring(10, 12) == month && UUserCard.substring(12, 14) <= day) {
        age++
      }
      return age
    }
  },
  rpxTopx(rpx) {
    return rpx / 750 * wx.getSystemInfoSync().windowWidth
  },
  throttle(fn, interval, options = { leading: true, trailing: false }) {
    // 1.记录上一次的开始时间
    const { leading, trailing, resultCallback } = options
    let lastTime = 0
    let timer = null

    // 2.事件触发时, 真正执行的函数
    const _throttle = function(...args) {
      return new Promise((resolve, reject) => {
        // 2.1.获取当前事件触发时的时间
        const nowTime = new Date().getTime()
        if (!lastTime && !leading) lastTime = nowTime

        // 2.2.使用当前触发的时间和之前的时间间隔以及上一次开始的时间, 计算出还剩余多长事件需要去触发函数
        const remainTime = interval - (nowTime - lastTime)
        if (remainTime <= 0) {
          if (timer) {
            clearTimeout(timer)
            timer = null
          }

          // 2.3.真正触发函数
          const result = fn.apply(this, args)
          resolve(result)
          // 2.4.保留上次触发的时间
          lastTime = nowTime
          return
        }

        if (trailing && !timer) {
          timer = setTimeout(() => {
            timer = null
            lastTime = !leading ? 0 : new Date().getTime()
            const result = fn.apply(this, args)
            resolve(result)
          }, remainTime)
        }
      })
    }

    _throttle.cancel = function() {
      if (timer) clearTimeout(timer)
      timer = null
      lastTime = 0
    }

    return _throttle
  }
}
