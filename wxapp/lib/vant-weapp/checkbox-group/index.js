import { VantComponent } from '../common/component';
VantComponent({
  field: true,
  relation: {
    name: 'checkbox',
    type: 'descendant',
    current: 'checkbox-group',
    linked(target) {
      this.updateChild(target);
    },
  },
  props: {
    max: Number,
    value: {
      type: <PERSON><PERSON>y,
      observer: 'updateChildren',
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      observer: 'updateChildren',
    },
  },
  methods: {
    updateChildren() {
      (this.children || []).forEach((child) => this.updateChild(child));
    },
    updateChild(child) {
      const { value, disabled } = this.data;
      child.setData({
        value: value.indexOf(child.data.name) !== -1,
        parentDisabled: disabled,
      });
    },
  },
});
