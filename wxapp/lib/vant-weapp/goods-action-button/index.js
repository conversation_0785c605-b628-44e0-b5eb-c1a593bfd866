import { VantComponent } from '../common/component';
import { link } from '../mixins/link';
import { button } from '../mixins/button';
import { openType } from '../mixins/open-type';
VantComponent({
  mixins: [link, button, openType],
  relation: {
    type: 'ancestor',
    name: 'goods-action',
    current: 'goods-action-button',
  },
  props: {
    text: String,
    color: String,
    loading: <PERSON><PERSON><PERSON>,
    disabled: <PERSON>olean,
    plain: Boolean,
    type: {
      type: String,
      value: 'danger',
    },
  },
  methods: {
    onClick(event) {
      this.$emit('click', event.detail);
      this.jumpLink();
    },
    updateStyle() {
      if (this.parent == null) {
        return;
      }
      const { children = [] } = this.parent;
      const { length } = children;
      const index = children.indexOf(this);
      this.setData({
        isFirst: index === 0,
        isLast: index === length - 1,
      });
    },
  },
});
