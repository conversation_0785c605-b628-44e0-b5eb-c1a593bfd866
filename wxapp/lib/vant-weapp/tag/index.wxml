<wxs src="../wxs/utils.wxs" module="utils" />

<view
  class="custom-class {{ utils.bem('tag', [type, size, { mark, plain, round }]) }}"
  style="{{ color && !plain ? 'background-color: ' + color + ';' : '' }}{{ textColor || (color && plain) ? 'color: ' + (textColor || color) : '' }}"
>
  <slot />
  <van-icon
    wx:if="{{ closeable }}"
    name="cross"
    custom-class="van-tag__close"
    bind:click="onClose"
  />
</view>
