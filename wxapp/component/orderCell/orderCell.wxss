/* pages/orderList/orderList.wxss */
@import "../../app.wxss";
.item {
  width: 100%;
  box-sizing: border-box;
  background: #fff;
  margin-top: 20rpx;
}
.item:last-child {
  margin-bottom: 0rpx;
}
.shopItem_img image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  border: 1rpx solid #eeeeee;
}
.orderContent_goods {
  height: 80rpx;
}
.prompt {
  padding-top: 8rpx;
  padding-bottom: 20rpx;
  text-align: right;
  color: #f06454;
}

.btn view {
  width: 160rpx;
  height: 56rpx;
  margin-left: 32rpx;
  border-radius: 8rpx;
}

.removeBtn {
  border: 2rpx solid #E4E4E4;
  color: #666;
}
.lookWl {
  border: 2rpx solid var(--themeColor);
  color: var(--themeColor);
}
.payBtn {
  background: var(--themeColor);
  border: 2rpx solid var(--themeColor);
  color: #fff;
}
.order_goods_encrypt {
  height: 120rpx;
  background: linear-gradient(270deg, #ffffff 0%, #f5f5f5 100%);
  border-radius: 8rpx;
}
.order_goods {
  background: #fafcff;
  border-radius: 8rpx;
}
