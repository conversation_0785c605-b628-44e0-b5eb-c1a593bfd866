<!-- component/orderCell/orderCell.wxml -->
<block wx:if='{{orderList.length}}'>
	<view class="p20 m20 bg-color-white container-radius" bindtap="onTap" data-item='{{item}}' wx:for='{{orderList}}'>
		<view class="flex_lr f28 c666">
			订单号：{{item.orderSn}}
			<view class="status f28 b">
				<text wx:if="{{item.status==1}}" class="f28" style="color:#F06454">待支付</text>
				<text wx:elif="{{item.status==2 ||item.status==6 || item.status==7 || item.status==8 || item.status==9 }}"
					class="f28" style="color:#FF9A46">待发货</text>
				<text wx:elif="{{item.status==3}}" class="f28" style="color:#2893FF">待收货</text>
				<text wx:elif="{{item.status==4}}" class="f28" style="color:#38BF87">已结束</text>
				<text wx:elif="{{item.status==5}}" class="f32" style="color:#999999">已取消</text>
				<!-- <text wx:elif="{{item.status==6}}" class="f32" style="color:#F06454">退货中</text>
				<text wx:elif="{{item.status==7}}" style="color:#999999" class="f32">已关闭</text> -->
			</view>
		</view>
		<!-- 中药药品 -->
		<view class="pt30" wx:if='{{item.drugType == 2}}'>
			<view class="order_goods_encrypt flex_lr_m pl15 pr15 mb20" wx:if="{{item.status==1||item.status==5}}">
				<view class="f24 c666">
					共{{item.items.length}}味药，支付后可查看处方
				</view>
				<view class="flex_tb">
					<view class="f28 c333 b flex_c_end">
						¥{{item.doseSalePrice}}
					</view>
					<view class="f28 c999 flex_c_end">
						x{{item.doseNum}}
					</view>
				</view>
			</view>
			<view class="order_goods pt20 pb20 pl15 pr15 flex_lr" wx:else>
				<view class="f28 c333 ell_more ell_2">
					<text wx:for="{{item.items}}" wx:for-item="key" wx:key="index">{{key.name}}</text>
				</view>
				<view class="flex_tb">
					<view class="f28 c333 b flex_c_end">
						¥{{item.doseSalePrice}}
					</view>
				</view>
			</view>
		</view>
		<!-- 西药药品 -->
		<view class="pt30" wx:else>
			<view class="shopItem flex_m" wx:for="{{item.items}}" wx:for-item="key" wx:key="index">
					<view class="shopItem_img">
						<image lazy-load src="{{key.icon}}" mode="aspectFill"></image>
					</view>
				<view class="flex_lr orderContent_goods flex1">
					<view class="f28 c333 ell_more ell_2 ml10 flex1">
						{{key.name}}
					</view>
					<view class="flex_tb">
						<view class="f28 c333 b flex_c_end">
							¥{{key.price}}
						</view>
						<view class="f28 c999 flex_c_end">
							x{{key.quantity}}
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="orderFot mt10">
			<view class="tr f28 c666">
				共{{item.quantity}}件商品 合计：<text class="c333 b">¥{{item.realPay}}</text>(含运费{{item.freight}}元)
			</view>
			<view wx:if="{{item.status==1}}">
				<view class="prompt f24">24小时内未支付，订单将自动取消</view>
				<view class="btn flex_c_end">
					<view class="removeBtn f26 flex_c_m" catchtap="onCancel" data-item='{{item}}'>取消订单</view>
					<view class="payBtn f26 flex_c_m" catchtap="onPay" data-item='{{item}}'>支付</view>
				</view>
			</view>
				<view class="btn mt20 flex_c_end" wx:if="{{item.status==3 || item.status==4}}">
					<view class="lookWl f26 flex_c_m" catchtap="onlook" data-item='{{item}}'>查看物流</view>
				</view>
		</view>
	</view>
</block>

<view wx:else class="flex_line_c no_msg_box">
	<image class="no_msg" src="{{static.nomes}}"></image>
	<view class="f28 c666">暂无数据</view>
</view>