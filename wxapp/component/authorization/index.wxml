	<van-popup show="{{ authShow }}" position="center" closeable bind:close="close">
		<view class="auth-content" wx:if='{{authShow}}' hover-class="none" hover-stop-propagation="false">
			<view class="flex_c_m f36 b pb30 bb1" hover-class="none" hover-stop-propagation="false">
				申请授权
			</view>
			<view class="c333 p20 f30" hover-class="none" hover-stop-propagation="false">
				{{doctorName}}医生提醒您：为了您更及时的接收医生消息,请您开通以下服务通知
			</view>
			<view class="c389AFF p20 f30 b" hover-class="none" hover-stop-propagation="false">
				接收小程序订阅消息
			</view>
			<view class="flex_m">
			<view class="auth-btn noAuth-btn bg-color-white c666  f32 flex_c_m flex1" bindtap='noAuthSub' hover-class="" hover-stop-propagation="false">
				暂不开通
			</view>
					<view class="auth-btn bg-color-primary cfff b f32 flex_c_m flex1" bindtap='authSub' hover-class="" hover-stop-propagation="false">
				立即开通
			</view>
			</view>
		</view>
	</van-popup>
	<hisToast id='histoast'></hisToast>