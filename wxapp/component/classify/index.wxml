<view>
  <van-grid column-num="5" border="{{false}}">
    <van-grid-item use-slot content-class="content-class" wx:for="{{list}}" wx:key="index">
      <view class="classify-item" data-item="{{item}}" bind:tap="goSearchClassify">
        <image class="icon" src="{{item.icon || img_default_classification}}" />
        <view class="text">{{item.name}}</view>
      </view>
    </van-grid-item>
    <van-grid-item wx:if="{{!showAll && allList.length > 10}}" use-slot content-class="content-class">
      <view class="classify-item" bind:tap="goAllClassify">
        <image class="icon" src="/static/images/<EMAIL>" />
        <view class="text">更多分类</view>
      </view>
    </van-grid-item>
  </van-grid>
</view>
