const api = require('../../config/api')
const util = require('../../utils/util')
Component({
  properties: {
    showAll: {
      type: Boolean,
      value: false
    }
  },
  data: {
    img_more_classification: api.ImgUrl + 'images/img_more_classification.png',
    img_default_classification: api.ImgUrl + 'images/<EMAIL>',
    list: [],
    allList: []
  },
  lifetimes: {
    attached: function() {

    },
    detached: function() {

    }
  },
  pageLifetimes: {
    show: function() {
      this.getData()
    }
  },
  methods: {
    getData() {
      util.request(api.categoryList, {}, 'post').then(res => {
        let list = res.data.data.slice()
        if (list.length > 10 && !this.properties.showAll) {
          list = list.splice(0, 9)
        }
        this.setData({
          list,
          allList: res.data.data
        })
      })
    },
    goAllClassify() {
      wx.navigateTo({
        url: '/pages/allClassify/index'
      })
    },
    goSearchClassify(e) {
      wx.navigateTo({
        url: '/pages/searchMerchandiseClassify/index?categoryId=' + e.currentTarget.dataset.item.id
      })
    }
  }
})
