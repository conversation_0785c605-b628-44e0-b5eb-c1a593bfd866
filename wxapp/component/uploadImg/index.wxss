/* component/uploadImg/index.wxss */
@import '../../app.wxss';
.upload{
  float:left;
  width:140rpx;
  height:140rpx;
  margin-top: 20rpx;
}
.upload image{
  display: block;
  width:100%;
  height:100%;
}
.picList .item{
  display: block;
  float:left;
  margin-right:40rpx;
  margin-top: 20rpx;
  width: 140rpx;
  height: 140rpx;
  position: relative;
  overflow: visible;
}
.picList .item .delPic{
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  right: -20rpx;
  top:-20rpx;
}
.picList .item .delPic image{
  display: block;
  width: 100%;
  height: 100%;
}
.picList .item>image{
  display: block;
  width:100%;
  height:100%;
}
.picList .item:nth-child(4n){
  margin-right: 0rpx;
}