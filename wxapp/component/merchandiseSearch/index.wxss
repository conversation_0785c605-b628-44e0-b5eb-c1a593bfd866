.relevant {
		position: fixed;
		left: 0;
		width: 100%;
		padding: 0 28rpx 28rpx 28rpx;
		background: #fff;
		z-index: 100;
		box-sizing: border-box;
		overflow: scroll;
}
.relevant-item {
		height: 100rpx;
		display: flex;
		align-items: center;
		border-bottom: 0.5rpx solid #eee;
    font-size: 32rpx;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    color: #333333
}
.search-icon {
		width: 44rpx;
		height: 44rpx;
		margin-right: 20rpx;
		flex-shrink: 0;
}
.match {
		color: #B4B4B4;
}
.i-search {
		height: 100rpx;
}
