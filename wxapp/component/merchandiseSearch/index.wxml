<view bind:tap="goSearch" id="searchWrap">
  <van-search
     value="{{ text }}"
     placeholder="{{placeholder}}"
     readonly="{{readonly}}"
     use-action-slot="{{showSearchBtn}}"
     use-left-icon-slot
     background="transparent"
     shape="{{shape}}"
     custom-class="i-search"
     bind:search="onSearch"
     bind:change="onChange"
     bind:clear="clearText"
  >
    <image  class="search-icon"  slot="left-icon" src="{{ic_input_search}}">搜索</image>
    <view wx:if="{{showSearchBtn}}" slot="action" bind:tap="onSearch">搜索</view>
  </van-search>
</view>
<view wx:if="{{showRelevant && showList}}" class="relevant" style="height: {{scrollViewHeight}}px">
  <view class="relevant-item" wx:for="{{relevantList}}" data-item="{{item}}" bind:tap="handleClick">
    <image class="search-icon" src="{{ic_input_search}}"></image>
    <rich-text nodes="{{item.matchText}}"></rich-text>
  </view>
</view>



