const api = require('../../config/api.js')
const util = require('../../utils/util')

// component/agreementPopup/index.js
Component({
  /**
	 * 组件的属性列表
	 */
  properties: {
    // 协议类型
    type: {
      type: String,
      value: ''
    }
  },

  /**
	 * 组件的初始数据
	 */
  data: {
    time: 5,
    on_off: false, //定时器开关
    isRead: false, //是否阅读置底部
    isClick: false, //点击同意
    showPopup: false,
    btntext: '请上滑看完此条款再同意',
    content: ''
  },
  async attached() {
    // await this.getArticle()
  },
  /**
	 * 组件的方法列表
	 */
  methods: {
    async getArticle() {
      try {
        const {
          data
        } = await util.request(`${api.getArticleDetail}/${this.data.type}`)
        if (data.code !== 0) {
          util.showToast({
            title: data.msg,
            icon: 'none',
            duration: 3000
          })
        }
        this.setData({
          content: data.data
        })
      } catch (error) {
        throw new Error(error)
      }
    },
    initTime() {
      if (this.data.on_off) {
        return
      }
      //定时开关 防止抖动触发多个定时器
      this.data.on_off = true
      this.interval = setInterval(() => {
        this.data.time--
        if (this.data.time <= 0) {
          this.data.isClick = true
          clearInterval(this.interval)
        }
        this.setData({
          time: this.data.time,
          isClick: this.data.isClick
        })
      }, 1000)
    },
    onScrolltolower() {
      this.setData({
        isRead: true,
        btntext: '同意'
      }, () => {
        this.initTime()
      })
      console.log('触底了。。。')
    },
    handleAgreement() {
      if (!this.data.isClick) {
        return
      }
      this.setData({
        showPopup: false
      })
      this.triggerEvent('agreement')
    },
    handleClose() {
      this.setData({
        showPopup: false
      })
      this.triggerEvent('closePopup')
    }
  }
})
