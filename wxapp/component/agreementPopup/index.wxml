<van-popup show="{{ showPopup }}" position="bottom" closeable bind:close="handleClose">

	<scroll-view scroll-y="true" bindscrolltolower='onScrolltolower' class="content bg-color-gray-light">
    <view class="tc f40 b mt20">{{content.title}}</view>
		<view class="p30">
			<rich-text nodes="{{content.content}}"></rich-text>
		</view>
	</scroll-view>
	<view class="footer p30">
		<view bind:tap="handleAgreement"
			class="footer-btn flex_c_m {{isRead?'bg-color-primary cfff':'bg-color-gray-light c999'}}">
			{{btntext}}<text wx:if="{{time>0 && isRead}}" class="second">({{ time }}s)</text>
		</view>
	</view>
</van-popup>