<view class="merchandise {{size}} {{horizontal ? 'horizontal' : ''}}" bind:tap="goDetail" data-detail="{{detail}}">
  <view class="image-wrap">
    <image  mode="aspectFit" src="{{detail.thumb || detail.icon}}"></image>
    <view wx:if="{{detail.status === 0}}" class="cover">
      已下架
    </view>
    <view wx:elif="{{detail.remainQuantity === 0}}" class="cover">
      无货
    </view>
    <view wx:elif="{{detail.quantity > detail.remainQuantity}}" class="cover">
      库存不足
    </view>
  </view>
  <view class="merchandise-content">
    <view class="title">
      <view class="ellipsis2">{{detail.name || detail.fullName}}</view>
      <view wx:if="{{subTitle}}" class="sub-title">{{detail.indications}}</view>
      <view wx:if="{{detail.quantity > detail.remainQuantity}}" class="sub-title-red">库存仅剩{{detail.remainQuantity}}件</view>
    </view>
    <view class="price-wrap">
      <view>¥{{detail.salePrice}}</view>
      <view catch:tap>
        <van-stepper wx:if="{{showStepper}}" value="{{ number }}" min="{{1}}" max="{{detail.quantity > detail.remainQuantity ? detail.quantity : detail.remainQuantity}}" bind:change="stepperChange"></van-stepper>
        <image wx:if="{{showCar}}" class="caricon" src="{{caricon}}" bind:tap="addCart"></image>
      </view>
      </view>
  </view>
</view>
<van-toast id="van-toast" />
