/* component/scrollTabs/index.wxss */
/* component/ArticleList/index.wxss */
@import "../../app.wxss";
.nav {
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  white-space: nowrap;
  display: flex;
  box-sizing: border-box;
	position: relative;
  z-index: 999;
}
.nav::-webkit-scrollbar {
  display: none;
}
.nav-item {
  flex-shrink: 0;
  -webkit-box-flex: 0;
  flex-grow: 0;
  font-size: 26rpx;
	display: inline-block;
  margin-right: 20rpx;
	padding: 5rpx 20rpx;
	background: #EFEFEF;
	font-size: 24rpx;
	color: #999999;
	border-radius: 24rpx;
}
.nav-item.active {
  color: cornflowerblue;
  border-bottom: 2px solid cornflowerblue;
}

.nav-active {
	background: #E6EFFF;
	color: var(--themeColor)!important;
}