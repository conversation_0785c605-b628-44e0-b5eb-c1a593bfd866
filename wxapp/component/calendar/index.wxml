<view class="flex b tb ac bg-color-white " wx:if="{{calendar}}">
  <view class="calendar b tb">
    <!-- 头部操作栏 -->
    <view wx:if="{{!calendarConfig.hideHeadOnWeekMode}}" class="handle {{calendarConfig.theme}}_handle-color fs28 b lr ac pc">
      <view class="flex_m prev fs36" wx:if="{{calendarConfig.showHandlerOnWeekMode || !calendarConfig.weekMode}}">
        <!-- <text class="prev-handle iconfont icon-doubleleft" bindtap="chooseDate" data-type="prev_year"></text> -->
        <!-- <text class="prev-handle iconfont icon-left" bindtap="chooseDate" data-type="prev_month"></text> -->
				<image class="icon-image" bindtap="chooseDate" data-type="prev_month" src="{{static.ic_arrow_left}}" /> 
      </view>
      <view class="date-in-handle b lr cc c333 f32" bindtap="doubleClickToToday">{{calendar.curYear || "--"}}年{{calendar.curMonth || "--"}}月</view>
      <view class="flex_m next fs36" wx:if="{{calendarConfig.showHandlerOnWeekMode || !calendarConfig.weekMode}}">
        <!-- <text class="next-handle iconfont icon-right" bindtap="chooseDate" data-type="next_month"></text> -->
					<image class="icon-image" bindtap="chooseDate" data-type="next_month" src="{{static.ic_arrow_right}}" /> 
        <!-- <text class="next-handle iconfont icon-doubleright" bindtap="chooseDate" data-type="next_year"></text> -->
      </view>
    </view>
    <!-- 星期栏 -->
    <view class="weeks b lr ac pt20 pb40 {{calendarConfig.theme}}_week-color">
      <view class="week f24" wx:for="{{calendar.weeksCh}}" wx:key="index" data-idx="{{index}}">{{item}}</view>
    </view>
    <!-- 日历面板主体 -->
    <view class="b lr wrap calendar-content"
        bindtouchstart="calendarTouchstart"
        catchtouchmove="calendarTouchmove"
        catchtouchend="calendarTouchend">
        <!-- 上月日期格子 -->
        <view
          class="grid b ac pc {{calendarConfig.theme}}_prev-month-date"
          wx:if="{{calendar.empytGrids}}"
          wx:for="{{calendar.empytGrids}}"
          wx:key="index"
          data-idx="{{index}}">
            <view class="date-wrap b cc">
              <view class="date">
                {{item.day}}
                <view
                  wx:if="{{calendarConfig.showLunar && item.lunar}}"
                  class="date-desc date-desc-bottom">
                    {{item.lunar.Term || item.lunar.IDayCn}}
                </view>
              </view>
            </view>
        </view>
        <!-- 本月日期格子 -->
        <view
          wx:for="{{calendar.days}}"
          wx:key="index"
          data-idx="{{index}}"
          data-date="{{item}}"
          bindtap="tapDayItem"
          class="grid {{item.class ? item.class  : ''}} {{calendarConfig.theme}}_normal-date b ac pc">
            <view
              class="date-wrap b cc {{(item.week === 0 || item.week === 6) ? calendarConfig.theme + '_weekend-color' : ''}}">
              <view class="date b ac pc flex_line_c_m {{item.class ? item.class  : ''}} {{calendarConfig.chooseAreaMode ? 'date-area-mode' : ''}} {{calendar.todoLabelCircle && item.showTodoLabel && !item.choosed ? calendarConfig.theme + '_todo-circle todo-circle' : '' }} {{item.isToday ? calendarConfig.theme + '_today' : ''}} {{item.choosed ? calendarConfig.theme + '_choosed' : ''}} {{item.disable ? calendarConfig.theme + '_date-disable' : ''}}">
                {{calendarConfig.markToday && item.isToday ? calendarConfig.markToday : item.day}}
                <view
                  wx:if="{{(calendarConfig.showLunar && item.lunar && !item.showTodoLabel) || (item.showTodoLabel && calendar.todoLabelPos !== 'bottom')}}"
                  class="date-desc {{calendarConfig.theme}}_date-desc date-desc-bottom {{(item.choosed || item.isToday) ? 'date-desc-bottom-always' : ''}} {{item.disable ? calendarConfig.theme + '_date-desc-disable' : ''}}">
                  {{item.lunar.Term || item.lunar.IDayCn}}
                </view>
                <view
                  wx:if="{{item.showTodoLabel && !calendar.todoLabelCircle || item.type === 1 || item.type === 2 || item.type === 3}}"
                  class="{{item.todoText ? 'date-desc' : calendarConfig.theme + '_todo-dot todo-dot'}} {{calendarConfig.showLunar ? calendarConfig.theme + '_date-desc-lunar' : ''}} {{calendar.todoLabelPos === 'bottom' ? 'date-desc-bottom todo-dot-bottom' : 'date-desc-top todo-dot-top'}} {{calendar.showLabelAlways && item.choosed && calendar.todoLabelPos === 'bottom' ? 'date-desc-bottom-always todo-dot-bottom-always' : ''}} {{calendar.showLabelAlways && item.choosed && calendar.todoLabelPos === 'top' ? 'date-desc-top-always todo-dot-top-always' : ''}}{{item.type == 1 ? 'elegant_active_1':''}}{{item.type == 2 ? 'elegant_active_2':''}}{{item.type == 3 ? 'elegant_active_3':''}}"
                  style="background-color: {{item.todoText ? '' : item.color || calendar.todoLabelColor}}; color: {{item.color}}">
                    {{item.todoText}}
                </view>
              </view>
            </view>
        </view>
        <!-- 下月日期格子 -->
        <view
          class="grid b ac pc {{calendarConfig.theme}}_next-month-date"
          wx:for="{{calendar.lastEmptyGrids}}"
          wx:key="index"
          data-idx="{{index}}">
          <view class="date-wrap b cc flex_line_c_m">
            <view class="date">
              {{item.day}}
              <view
                wx:if="{{calendarConfig.showLunar && item.lunar}}"
                class="date-desc date-desc-bottom">
                  {{item.lunar.Term || item.lunar.IDayCn}}
              </view>
            </view>
          </view>
        </view>
      </view>
  </view>
</view>