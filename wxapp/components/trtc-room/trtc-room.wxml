<import src='./template/1v1/1v1.wxml'/>
<import src='./template/grid/grid.wxml'/>
<import src='./template/custom/custom.wxml'/>

<view class="trtc-room-container {{isFullscreenDevice?'fullscreen-device-fix':''}}">
  <block wx:if="{{template === '1v1'}}">
    <template is='1v1' data="{{pusher, streamList, debug, enableIM}}"></template>
  </block>
  <block wx:if="{{template === 'grid'}}">
    <template is='grid' data="{{pusher, streamList, visibleStreamList, debug, enableIM, panelName, gridPagePlaceholderStreamList, gridPageCount, gridCurrentPage, gridPlayerPerPage, isShowMoreMenu, MICVolume, BGMVolume, BGMProgress, beautyStyle, beautyStyleArray, filterIndex, filterArray, audioReverbTypeArray,playTiem,imageList,ImgUrl,config}}"></template>
  </block>
  <block wx:if="{{template === 'custom'}}">
    <template is='custom' data="{{pusher, streamList, debug}}"></template>
  </block>

  <view class="im-panel" wx:if="{{enableIM && showIMPanel}}">
    <view class="message-panel-body">
      <scroll-view scroll-y="true" class="message-scroll-container" scroll-into-view="message{{messageList.length-1}}" scroll-with-animation="{{true}}">
        <view class="message-list">
          <view class="message-item" wx:for="{{messageList}}" wx:key="index" id="message{{index}}">
            <span class="user-name {{item.name == config.userID?'mine':''}}">{{item.name}}</span>
            <span class="message-content">{{item.message}}</span>
          </view>
          <view id="message-bottom"></view>
        </view>
      </scroll-view>
        
    </view>
    <view class="message-panel-bottom">
      <view class="message-input-container">
        <input class="message-input" type="text" value="{{messageContent}}" bindinput='_inputIMMessage' bindconfirm='_sendIMMessage' confirm-type="send" placeholder="请输入消息" maxlength="200" placeholder-style="color:#ffffff;opacity: 0.55;"/>
      </view>
      <view class="message-send-btn">
        <button class="btn" bindtap="_sendIMMessage" hover-class="btn-hover">发送</button>
      </view>
    </view>
    <view bindtap="_toggleIMPanel" class='close-btn'>X</view>
  </view>

  <view class="debug-info-btn {{debugMode && !debugPanel?'':'none'}}">
    <button class="debug-btn" bindtap="_debugTogglePanel" hover-class="button-hover">Debug</button>
  </view>
  <view class="debug-info {{debugMode && debugPanel?'':'none'}}">
    <view bindtap="_debugTogglePanel" class='close-btn'>X</view>
    <view>appVersion: {{appVersion}}</view>
    <view>libVersion: {{libVersion}}</view>
    <view>template: {{template}}</view>
    <view>debug: <button class="{{debug?'':'false'}} debug-btn" bindtap="_debugToggleVideoDebug" hover-class="button-hover">{{debug}}</button></view>
    <view>userID: {{pusher.userID}}</view>
    <view>roomID: {{pusher.roomID}}</view>
    <view>camera: <button class="{{pusher.enableCamera?'':'false'}} debug-btn" bindtap="_toggleVideo" hover-class="button-hover">{{pusher.enableCamera}}</button></view>
    <view>mic: <button class="{{pusher.enableMic?'':'false'}} debug-btn" bindtap="_toggleAudio" hover-class="button-hover">{{pusher.enableMic}}</button></view>
    <view>switch camera: <button class="debug-btn" bindtap="switchCamera" hover-class="button-hover">{{cameraPosition||pusher.frontCamera}}</button></view>
    <view>Room:
      <button class="debug-btn" bindtap="_debugEnterRoom" hover-class="button-hover">Enter</button>
      <button class="debug-btn" bindtap="_debugExitRoom" hover-class="button-hover">Exit</button>
      <button class="debug-btn" bindtap="_debugGoBack" hover-class="button-hover">Go back</button>
    </view>
    <view>IM: <button class="debug-btn" bindtap="_debugSendRandomMessage" hover-class="button-hover">send</button></view>
    <view>user count: {{userList.length}}</view>
    <view wx:for="{{userList}}" wx:key="userID">{{item.userID}}|
      mainV:<span class="text {{item.hasMainVideo? 'true' : 'false' }}">{{item.hasMainVideo||false}}</span>|
      mainA:<span class="text {{item.hasMainAudio? 'true' : 'false' }}">{{item.hasMainAudio||false}}</span>|
      auxV:<span class="text {{item.hasAuxVideo? 'true' : 'false' }}">{{item.hasAuxVideo||false}}</span></view>
    <view>stream count: {{streamList.length}}</view>
    <view wx:for="{{streamList}}" wx:key="streamID">{{item.userID}}|{{item.streamType}}|
      SubV:<button class="{{!item.muteVideo?'':'false'}} debug-btn" bindtap="_debugToggleRemoteVideo" hover-class="button-hover" data-user-i-d="{{item.userID}}" data-stream-type="{{item.streamType}}">{{!item.muteVideo}}</button>|
      SubA:<button class="{{!item.muteAudio?'':'false'}} debug-btn" bindtap="_debugToggleRemoteAudio" hover-class="button-hover" data-user-i-d="{{item.userID}}" data-stream-type="{{item.streamType}}">{{!item.muteAudio}}</button></view>
  </view>
</view>
