
@import "./template/1v1/1v1.wxss";
@import "./template/grid/grid.wxss";
@import "./template/custom/custom.wxss";

.pusher {
  width: 100%;
  height: 100%;
}

.player {
  width: 100%;
  height: 100%;
}

.debug-info{
  max-width: 100vw;
  max-height: 90vh;
  box-sizing: border-box;
  overflow-y: scroll;
  position: absolute;
  z-index: 9999;
  background-color: rgba(0, 0, 0, .5);
  color: #fff;
  bottom: 20rpx;
  left: 0;
  padding: 10rpx;
  font-size: 12px;
}
.debug-info-btn .debug-btn,
.debug-info .debug-btn{
  padding: 0 8px;
  min-height: 18px;
  width: auto;
  font-size: 12px;
  line-height: 18px;
  display: inline-block;
  color: #06ae56;
  background-color: #f2f2f2;
}
.debug-info .debug-btn.false{
  color: rgb(114, 114, 114);
}
.debug-info-btn .debug-btn,
.debug-info .button-hover {
  background-color: rgb(219, 219, 219);
}
.debug-info .close-btn{
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px 10px;
}
.debug-info .text.true{
  color: #1fff8b;
}
.debug-info .text.false{
  color: #ff2e2e;
}
.debug-info-btn{
  position: absolute;
  z-index: 9998;
  bottom: 160rpx;
  left: 0;
}

.trtc-room-container .btn {
  display: inline-block;
  width: auto;
  height: 60rpx;
  min-height: 60rpx;
  line-height: 60rpx;
  font-size: 12px;
  font-weight: normal;
  padding: 0 10rpx;
  color: #006eff;
  background-color: #f2f2f2;
  margin: 0 16rpx;
}
.trtc-room-container .btn.active{
  color: #f2f2f2;
  background-color: #006eff;
}
.trtc-room-container .btn-hover{
  background-color: #d1d1d1;
}

.im-panel{
  position: absolute;
  z-index: 9;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 90vw;
  height: 320rpx;
  top: 50vh;
  left: 50vw;
  transform: translate(-50%, -50%);
  padding: 20rpx 0;
  border-radius: 10rpx;
  font-size: 12px;
  /* bottom: 25vh; */
  color: #fff;
  background-color: rgba(0, 0, 0, 0.8);
}
.im-panel .close-btn {
  position: absolute;
  top: 0;
  right: -3px;
  padding: 5px 10px;
  z-index: 99;
}
.message-panel-body{
  width: 100%;
  height: 80%;
  overflow-x: hidden;
  overflow-y: scroll;
}
.message-scroll-container{
  height: 100%;
  /* box-sizing: border-box;
  padding: 0 20rpx; */
}
.message-list{
  width: 100%;
  box-sizing: border-box;
  padding: 0 20rpx;
  /* display: flex;
  flex-direction: column; */
}
.message-item{
  width: 100%;
  /* height: 36rpx; */
  /* padding: 0 20rpx; */
  padding-bottom: 10rpx;
  display: flex;
  flex-direction: row;
}
.message-item .user-name{
  width: 20%;
  color: #2483ff;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.user-name.mine{
  color: #ff7424;
}
.message-item .separate{
  padding:0 5px;
  color: #fff;
}
.message-item .message-content{
  word-wrap:break-word; 
  word-break:break-all;
  padding-left: 20rpx;
  position: relative;
  max-width: 80%; 
  box-sizing: border-box;
}
.message-content::after{
  content: ':';
  position: absolute;
  left: 0;
  top: 0;
}
.message-panel-bottom{
  width: 100%;
  height: 50rpx;
  box-sizing: border-box;
  padding: 0 20rpx 0;
  margin-top: 20rpx;
  display: flex;
  flex-direction: row;
}
.message-input-container {
  flex-grow: 1;
}
.message-input-container .message-input {
  font-size: 12px;
  padding-left: 20rpx;
  border-radius: 10rpx;
  height: 100%;
  background-color: rgba(0,0,0,0.1);
}
.message-send-btn .btn{
  margin-right: 0;
  height: 50rpx;
  min-height: 50rpx;
  line-height: 50rpx;
}

.volume-animation{
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  left: 0;
  top: initial;
  bottom: 20rpx;
  z-index: 9;
  /* transform: translate(-50%, 0); */
}
.volume-animation .image{
  position: absolute;
  width: 80rpx;
  height: 80rpx;
}
.volume-animation .audio-active{
  animation: viewlinear 1.5s linear infinite;
} 
@keyframes viewlinear {
  
  /** 第一种写法**/
  0% {
    height: 0;
  }
  100% {
    height: 100%;
  }
}

.none,
.view-container.none,
.template-grid .view-container.none,
.template-1v1 .view-container.none{
  display: none !important;
}
