<!-- template grid -->
<template name='grid'>
	<view class="template-grid">
		<view class="column-layout">
			<view class="column-1">
				<view class="grid-scroll-container" bindtouchstart="_handleGridTouchStart" bindtouchend="_handleGridTouchEnd">
					<!-- <view id="grid-container-id" class="grid-container {{visibleStreamList.length < 4 ? 'stream-' + visibleStreamList.length : visibleStreamList.length%2 == 0? 'stream-odd':'stream-even'}}"> -->
					<view id="grid-container-id"
						class="grid-container {{visibleStreamList.length < 4 ? 'stream-' + visibleStreamList.length : 'stream-3'}}">

						<view
							class="view-container pusher-container {{pusher.isVisible && ((gridCurrentPage === 1 && gridPlayerPerPage > 3) || gridPlayerPerPage < 4)?'':'none'}}">
							<live-pusher class="pusher" url="{{pusher.url}}" mode="{{pusher.mode}}" autopush="{{pusher.autopush}}"
								enable-camera="{{pusher.enableCamera}}" enable-mic="{{pusher.enableMic}}" muted="{{!pusher.enableMic}}"
								enable-agc="{{pusher.enableAgc}}" enable-ans="{{pusher.enableAns}}"
								enable-ear-monitor="{{pusher.enableEarMonitor}}" auto-focus="{{pusher.enableAutoFocus}}"
								zoom="{{pusher.enableZoom}}" min-bitrate="{{pusher.minBitrate}}" max-bitrate="{{pusher.maxBitrate}}"
								video-width="{{pusher.videoWidth}}" video-height="{{pusher.videoHeight}}"
								beauty="{{pusher.beautyLevel}}" whiteness="{{pusher.whitenessLevel}}"
								orientation="{{pusher.videoOrientation}}" aspect="{{pusher.videoAspect}}"
								device-position="{{pusher.frontCamera}}" remote-mirror="{{pusher.enableRemoteMirror}}"
								local-mirror="{{pusher.localMirror}}" background-mute="{{pusher.enableBackgroundMute}}"
								audio-quality="{{pusher.audioQuality}}" audio-volume-type="{{pusher.audioVolumeType}}"
								audio-reverb-type="{{pusher.audioReverbType}}" waiting-image="{{pusher.waitingImage}}" debug="{{debug}}"
								beauty-style="{{pusher.beautyStyle}}" filter="{{pusher.filter}}"
								bindstatechange="_pusherStateChangeHandler" bindnetstatus="_pusherNetStatusHandler"
								binderror="_pusherErrorHandler" bindbgmstart="_pusherBGMStartHandler"
								bindbgmprogress="_pusherBGMProgressHandler" bindbgmcomplete="_pusherBGMCompleteHandler"
								bindaudiovolumenotify="_pusherAudioVolumeNotify" />
							<view class="no-video" wx:if="{{!pusher.enableCamera}}">
								<image class="image" src="./static/mute-camera-white.png"></image>
							</view>
						</view>

						<view wx:for="{{visibleStreamList}}" wx:key="streamID"
							class="view-container player-container {{item.isVisible?'':'none'}}" id="{{'player-'+item.streamID}}"
							data-userid="{{item.userID}}" data-streamtype="{{item.streamType}}" bindtap="_doubleTabToggleFullscreen">
							<live-player class="player" id="{{item.streamID}}" data-userid="{{item.userID}}"
								data-streamid="{{item.streamID}}" data-streamtype="{{item.streamType}}" src="{{item.src}}" mode="RTC"
								autoplay="{{item.autoplay}}" mute-audio="{{item.muteAudio}}" mute-video="{{item.muteVideo}}"
								orientation="{{item.orientation}}" object-fit="{{item.objectFit}}"
								background-mute="{{item.enableBackgroundMute}}" min-cache="{{item.minCache}}"
								max-cache="{{item.maxCache}}" sound-mode="{{item.soundMode}}"
								enable-recv-message="{{item.enableRecvMessage}}" auto-pause-if-navigate="{{item.autoPauseIfNavigate}}"
								auto-pause-if-open-native="{{item.autoPauseIfOpenNative}}" debug="{{debug}}"
								bindstatechange="_playerStateChange" bindfullscreenchange="_playerFullscreenChange"
								bindnetstatus="_playerNetStatus" bindaudiovolumenotify="_playerAudioVolumeNotify" />
							<view class="no-video" wx:if="{{item.muteVideo}}">
								<image class="image" src="./static/display-pause-white.png"></image>
								<!-- <view class="text">
                  <p>{{item.userID}}</p>
                </view> -->
							</view>
							<view class="no-video" wx:if="{{!item.hasVideo && !item.muteVideo}}">
								<image class="image" src="./static/mute-camera-white.png"></image>
								<!-- <view class="text">
                  <p>{{item.userID}}</p>
                </view> -->
								<view class="text">
									<p>对方摄像头未打开</p>
								</view>
							</view>
						</view>

						<view wx:for="{{gridPagePlaceholderStreamList}}" wx:key="id"
							class="view-container player-container player-placeholder">
							<image class="image" src="./static/mute-camera-white.png"></image>
						</view>
					</view>
				</view>
				<view class="seconds-box f32 b">
					{{playTiem == 0 ? '00:00' : playTiem}}
				</view>
				<view class="handle-btn-box">
					<view class="menu-item1" bindtap="_hangUp">
						<image src="./static/images/btn_hang_up.png"></image>
						<view class="cfff tc f20 nowrap mt15">离开诊室</view>
					</view>
					<view class="menu-item1" bindtap="_toggleAudio">
						<image
							src="{{pusher.enableMic? './static/images/btn_microphone.png': './static/images/btn_microphone_selected.png'}}">
						</image>
						<view class="cfff tc f20 nowrap mt15">静音</view>
					</view>
					<view class="menu-item1" bindtap="_toggleVideo">
						<image
							src="{{pusher.enableCamera? './static/images/btn_camera_open.png': './static/images/btn_camera_close.png'}}">
						</image>
						<view class="cfff tc f20 nowrap mt15">摄像头{{pusher.enableCamera?'已开':'已关'}}</view>
					</view>
					<view class="menu-item1" bindtap="_toggleSoundMode">
						<image
							src="{{streamList[0].soundMode === 'ear' ? './static/images/btn_voice.png': './static/images/btn_voice_selected.png'}}">
						</image>
						<view class="cfff tc f20 nowrap mt15">免提</view>
					</view>
					<view class="menu-item1" bindtap="switchCamera">
						<image src="./static/images/btn_camera.png"></image>
						<view class="cfff tc f20 nowrap mt15">翻转摄像头</view>
					</view>
					<view class="menu-item1" bindtap="chooseAvatar">
						<image src="./static/images/btn_image_img.png"></image>
						<view class="cfff tc f20 nowrap mt15">发送图片</view>
					</view>

				</view>
			</view>
			<view class="column-2">
				<view class="pt30 pb30 flex_c f24 c999">
					医患图片记录
				</view>
				<view class="scroll-x-box flex" wx:if='{{imageList.length>0}}'>
					<view class="scroll-item flex_line_c_m pr" wx:for='{{imageList}}'>
						<image src='{{item.path}}' mode="aspectFill" wx:if="{{item.roomId == config.roomID}}" bindtap="previewImage"
							data-index='{{index}}' />
						<view class="c999 pt10">{{item.name}}</view>
					</view>
				</view>
				<view class="c666 f24 flex_line_c mt50" wx:else>
					暂无数据~
				</view>
			</view>

		</view>
		<view class="panel bgm-panel {{panelName === 'bgm-panel' ? '' : 'none'}}">
			<view bindtap="_handleMaskerClick" class='close-btn'>X</view>
			<view class="panel-header">背景音乐</view>
			<view class="panel-body">
				<view class="setting-option">
					<view class="label">MIC音量</view>
					<view class="slider-content">
						<slider value="{{MICVolume}}" min="0" max="100" show-value="true" activeColor="#006eff"
							bindchange="_changeProperty" data-property-name="MICVolume" />
					</view>
				</view>
				<view class="setting-option">
					<view class="label">BGM音量</view>
					<view class="slider-content">
						<slider value="{{BGMVolume}}" min="0" max="100" show-value="true" activeColor="#006eff"
							bindchange="_changeProperty" data-property-name="BGMVolume" />
					</view>
				</view>
				<view class="setting-option">
					<view class="label">播放进度</view>
					<view class="slider-content">
						<progress activeColor="#006eff" percent="{{BGMProgress}}"></progress>
					</view>
				</view>
				<view class="menu">
					<view class="menu-item" bindtap="_handleBGMOperation" data-operation-name="playBGM">
						<view class="label">播放</view>
					</view>
					<view class="menu-item" bindtap="_handleBGMOperation" data-operation-name="pauseBGM">
						<view class="label">暂停</view>
					</view>
					<view class="menu-item" bindtap="_handleBGMOperation" data-operation-name="resumeBGM">
						<view class="label">继续</view>
					</view>
					<view class="menu-item" bindtap="_handleBGMOperation" data-operation-name="stopBGM">
						<view class="label">停止</view>
					</view>
				</view>
			</view>
		</view>
		<view class="masker {{panelName =='' ? 'none' : ''}}" bindtap="_handleMaskerClick"></view>
	</view>
</template>