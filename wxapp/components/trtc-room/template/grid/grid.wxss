@import "../../../../app.wxss";
/* 9人 会议模版 */
.template-grid{
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  /* display: flex;
  flex-direction: row;
  flex-wrap: wrap; */
}
.column-layout{
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.column-layout .column-1{
  flex: 1;
  position: relative;
}
.column-layout .column-2{
  position: relative;
  height: 262rpx;
  background: #000;
}

.fullscreen-device-fix .column-layout .column-2{
  height: 262rpx;
}
.menu {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.menu .menu-item{
  text-align: center;
  height: 100rpx;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.menu .menu-item .image{
  width: 46rpx;
  height: 46rpx;
}
.more-menu {
  position: absolute;
  top: 0;
}
.more-menu .scroll-container{
  width: 100%;
  height: 100rpx;
  white-space: nowrap;
}
.more-menu .menu-item-container{
  width: 20%;
  display: inline-block;
}

.template-grid .grid-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.template-grid .grid-scroll-container{
  width: 100%;
  height: 100%;
  /* box-sizing: border-box; */
  /* overflow-y: scroll; */
  background-color: #000;
}
.grid-containe.overflow {
  height: auto;
}
.template-grid .view-container {
  position: relative;
}

.stream-0 .view-container{
   width: 100%;
   height: 100%;
}

.stream-1 .view-container{
   width: 100%;
   height: 50%;
}

.stream-2 .view-container{
   width: 50%;
   height: 50%;
}

.stream-2 .view-container:nth-child(1){
   width: 100%;
   height: 50%;
}

.stream-3 .view-container{
   width: 50%;
   height: 50%;
}

.stream-4 .view-container{
   width: 50%;
   height: 33.3%;
}

.stream-4 .view-container:nth-child(1){
   width: 100%;
   height: 33.3%;
}

.stream-5 .view-container {
   width: 50%;
   height: 33.3%;
}

.stream-6 .view-container{
   width: 33.3%;
   height: 33.3%;
}

.stream-6 .view-container:nth-child(1){
   width: 100%;
   height: 33.3%;
}

.stream-7 .view-container{
   width: 33.3%;
   height: 33.3%;
}

.stream-7 .view-container:nth-child(1){
   width: 50%;
   height: 33.3%;
}

.stream-7 .view-container:nth-child(2){
   width: 50%;
   height: 33.3%;
}

.stream-8 .view-container{
   width: 33.3%;
   height: 33.3%;
}

.stream-even .view-container{
  width: 50%;
  height: 50%;
}

.stream-odd .view-container{
  width: 50%;
  height: 50%;
}
.stream-odd .view-container:last-child{
  width: 100%;
  height: 50%;
}

.template-grid .operation-bar {
  position: absolute;
  bottom: 6rpx;
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.operation-bar .operation-item-container {
  width: auto;
  display: flex;
  flex-direction: row;
  justify-content: center;
  background: rgb(0, 0, 0, .3);
  border-radius: 10rpx;
} 
.template-grid .operation-bar .operation-item {
  width: 64rpx;
  height: 64rpx;
  /* flex-grow: 1; */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.operation-item .item-image{
  width: 36rpx;
  height: 36rpx;
}
.template-grid .volume-progress {
  width: 100%;
  position: absolute;
  bottom: 0;
}

.template-grid .btn-normal {
  width: 64rpx;
  height: 64rpx;
  margin: 0 6rpx;
  box-sizing: border-box;
  display: flex;
  background: rgba(255, 255, 255, 1);
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}

.template-grid .btn-normal .btn-image{
  width: 36rpx;
  height: 36rpx;
}

.template-grid .btn-hangup {
  background: #f75c45;
}

.template-grid .panel{
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  width: 90vw;
  height: auto;
  z-index: 999;
  top: 50vh;
  left: 50vw;
  transform: translate(-50%, -50%);
  color: white;
  display: flex;
  flex-direction: column;
  padding: 20rpx 0;
  border-radius: 10rpx;
  box-sizing: border-box;
  font-size: 14px;
}
.panel .close-btn {
  position: absolute;
  top: 0;
  right: 0;
  padding: 5px 10px;
}
.panel .panel-header{
  text-align: center;
  padding-bottom: 20rpx;
}
.panel .panel-tips {
  color: #999;
  text-align: center;
}
.panel .panel-body{
  flex: 1;
  max-height: 50vh;
}
.panel .panel-body .scroll-container{
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.memberlist-panel .panel-body{
  height: 50vh;
}
.memberlist-panel .member-item {
  display: flex;
  /* border-bottom: 1px solid #999; */
  margin: 16rpx 16rpx 16rpx 32rpx;
}
.memberlist-panel .member-id {
  width: 30%;
  font-size: 12px;
  line-height: 64rpx;
}
.memberlist-panel .member-btns{
  width: 70%;
  display: flex;
  justify-content: flex-end;
}
.memberlist-panel .member-btns .btn-normal{
  margin-left: 0;
}
.memberlist-panel .member-btns .btn{
  margin-right: 0;
}

.setting-panel .panel-body{
  height: 50vh;
}
.setting-panel .setting-option{
  display: flex;
  justify-content: space-between;
  margin: 16rpx 16rpx 16rpx 32rpx;
  /* box-sizing: border-box;
  padding: 12rpx 16rpx 12rpx 32rpx; */
}
.setting-panel .setting-option .label{
  /* line-height: 64rpx; */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.setting-panel .setting-switch {
  transform:scale(0.8);
  margin-right: -12rpx;
}

.bgm-panel .panel-body{
  height: auto;
}
.bgm-panel .setting-option{
  height: 60rpx;
  display: flex;
  flex-direction: row;
  margin: 16rpx 16rpx 16rpx 32rpx;
}
.bgm-panel .setting-option .label{
  width: 140rpx;
  line-height: 60rpx;
}
.bgm-panel .setting-option .slider-content{
  flex: 1;
  line-height: 60rpx;
}
.bgm-panel .setting-option .slider-content slider{
  transform:scale(0.9);
  margin: 0;
}

.bgm-panel .setting-option .slider-content progress{
  transform:scale(0.9);
  margin-top: 28rpx;
}
.bgm-panel .menu {
  padding: 16rpx 32rpx 16rpx 32rpx;
  box-sizing: border-box;
}
.bgm-panel .menu .menu-item{
  height: 80rpx;
  background-color: #333;
}

.template-grid .masker{
  position: absolute;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
}

.template-grid .no-stream,
.template-grid .no-video{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  color:#fff;
  background-color: rgba(0, 0, 0, 0.4);
  font-size: 12px;
}
.template-grid .audio-volume,
.template-grid .no-audio{
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  width: 36rpx;
  height: 36rpx;
}

.no-stream .image,
.no-video .image{
  width: 60rpx;
  height: 60rpx;
}

.audio-volume .image,
.no-audio .image{
  width: 36rpx;
  height: 36rpx;
  position: absolute; /*android 的bug ，image absolute后会向上漂移几个像素，如果要对其必须都设置absolute*/
}

.audio-active {
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 0;
  overflow: hidden;
}
.audio-active .image{
  bottom: 0;
}

.slide-up-tips {
  position: absolute;
  bottom: -100rpx;
  left: 50%;
  transform: translate(-50%, 0);
  width: 200rpx;
  height: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-size: 12px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.4);
  box-sizing: border-box;
  padding: 20rpx;
  border-radius: 10rpx;
  opacity: 0;
}
.slide-up-tips .image {
  width: 100rpx;
  height: 100rpx;
}
.player-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.player-placeholder .image {
  width: 100rpx;
  height: 100rpx;
}

.pages-container {
  width: auto;
  left: 50%;
  transform: translate(-50%, 0);
  height: 20rpx;
  position: absolute;
  bottom: 12%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.pages-container .page-item {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  margin: 0 8rpx;
  background-color: rgb(99, 99, 99, .5);
}
.pages-container .page-item.current {
  background-color: #fff;
}

.radio-group-no-box {
	display: inline-block;
	color: #006eff;
	background-color: #ffffff;
	border: 1px solid #006eff;
	border-radius: 4px;
  margin-left: 180rpx;
  font-size: 12px;
}
.radio-group-no-box .radio-item{
	padding: 5px 8px;
	text-align: center;
	border-right: 1px solid #006eff;
	display: inline-block;
}
.radio-group-no-box .radio-item:last-child{
	border-right: none;
}
.radio-group-no-box .radio-item.selected{
	color: #ffffff;
	background-color: #006eff;
}
.radio-group-no-box radio{
	display: none;
}

.picker-label{
  display: inline-block;
  color: #006eff;
	background-color: #ffffff;
	border: 1px solid #006eff;
  border-radius: 4px;
  padding: 5px 8px;
  text-align: center;
  font-size: 12px;
}


/* 新增样式 */
.handle-btn-box{
  width: 100%;
  position: absolute;
  bottom: 20rpx;
  left: 0;
  display: flex;
  padding:0 40rpx;
  justify-content: space-between;
	align-items: center;
	/* background: red; */
}
.handle-btn-box .menu-item1{
	display: flex;
	flex-direction: column;
	align-items: center;
}
.handle-btn-box .menu-item2{
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
}
.handle-btn-box .menu-item-active{
  width: 98rpx;
  height: 96rpx;
  border-radius: 50%;
}
.handle-btn-box .menu-item1 image{
	width: 72rpx;
  height: 72rpx;
}
.handle-btn-box .menu-item2 image{
 width: 100%;
 height: 100%;
}
.handle-btn-box .menu-item-active image{
 width: 100%;
 height: 100%;
}

.seconds-box{
  width: 100%;
  position: absolute;
  bottom: 160rpx;
  left: 0;
  color: #FFFFFF;
  text-align: center;
  font-size:32rpx;
  /* background: chartreuse; */

}
.scroll-x-box{
  overflow: auto;
  margin: 0 0 0 32rpx;
}
.scroll-item{
  font-size: 20rpx;
  margin: 0 32rpx 0 0;
}
.scroll-item image{
  width: 96rpx;
  height: 96rpx;
  border-radius: 10rpx;
}