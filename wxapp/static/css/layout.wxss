.m0 {
  margin: 0;
}
.mt0{
	margin-top: 0;
}
.mb0{
	margin-top: 0;
}
.ml0{
	margin-top: 0;
}
.mr0{
	margin-top: 0;
}
.m5 {
  margin: 5rpx;
}
.m10 {
  margin: 10rpx;
}
.m15 {
  margin: 15rpx;
}
.m20 {
  margin: 20rpx;
}
.m25 {
  margin: 25rpx;
}
.m30 {
  margin: 30rpx;
}
.m40 {
  margin: 40rpx;
}
.m50 {
  margin: 50rpx;
}
.mla {
  margin-left: auto;
}
.ml5 {
  margin-left: 5rpx;
}
.ml10 {
  margin-left: 10rpx;
}
.ml15 {
  margin-left: 15rpx;
}
.ml20 {
  margin-left: 20rpx;
}
.ml25 {
  margin-left: 25rpx;
}
.ml30 {
  margin-left: 30rpx;
}
.ml35 {
  margin-left: 35rpx;
}
.ml40 {
  margin-left: 40rpx;
}
.ml45 {
  margin-left: 45rpx;
}
.ml50 {
  margin-left: 50rpx;
}
.mra {
  margin-right: auto;
}

.mr5 {
  margin-right: 5rpx;
}
.mr10 {
  margin-right: 10rpx;
}
.mr15 {
  margin-right: 15rpx;
}
.mr20 {
  margin-right: 20rpx;
}
.mr25 {
  margin-right: 25rpx;
}
.mr30 {
  margin-right: 30rpx;
}
.mr35 {
  margin-right: 35rpx;
}
.mr40 {
  margin-right: 40rpx;
}
.mr45 {
  margin-right: 45rpx;
}
.mr50 {
  margin-right: 50rpx;
}
.mr60 {
  margin-right: 60rpx;
}

.mta {
  margin-top: auto;
}
.mt5 {
  margin-top: 5rpx;
}
.mt10 {
  margin-top: 10rpx;
}
.mt15 {
  margin-top: 15rpx;
}
.mt20 {
  margin-top: 20rpx;
}
.mt25 {
  margin-top: 25rpx;
}
.mt30 {
  margin-top: 30rpx;
}
.mt35 {
  margin-top: 35rpx;
}
.mt40 {
  margin-top: 40rpx;
}
.mt45 {
  margin-top: 45rpx;
}
.mt50 {
  margin-top: 50rpx;
}

.mb5 {
  margin-bottom: 5rpx;
}
.mb10 {
  margin-bottom: 10rpx;
}
.mb15 {
  margin-bottom: 15rpx;
}
.mb20 {
  margin-bottom: 20rpx;
}
.mb25 {
  margin-bottom: 25rpx;
}
.mb30 {
  margin-bottom: 30rpx;
}
.mb35 {
  margin-bottom: 35rpx;
}
.mb40 {
  margin-bottom: 40rpx;
}
.mb45 {
  margin-bottom: 45rpx;
}
.mb50 {
  margin-bottom: 50rpx;
}

.p0 {
  padding: 0;
}

.p5 {
  padding: 5rpx;
}
.pt5 {
  padding-top: 5rpx;
}
.pr5 {
  padding-right: 5rpx;
}
.pb5 {
  padding-bottom: 5rpx;
}
.pl5 {
  padding-left: 5rpx;
}

.p10 {
  padding: 10rpx;
}
.pt10 {
  padding-top: 10rpx;
}
.pr10 {
  padding-right: 10rpx;
}
.pb10 {
  padding-bottom: 10rpx;
}
.pl10 {
  padding-left: 10rpx;
}

.p15 {
  padding: 15rpx;
}
.pt15 {
  padding-top: 15rpx;
}
.pr15 {
  padding-right: 15rpx;
}
.pb15 {
  padding-bottom: 15rpx;
}
.pl15 {
  padding-left: 15rpx;
}

.p20 {
  padding: 20rpx;
}
.pt20 {
  padding-top: 20rpx;
}
.pr20 {
  padding-right: 20rpx;
}
.pb20 {
  padding-bottom: 20rpx;
}
.pl20 {
  padding-left: 20rpx;
}

.p25 {
  padding: 25rpx;
}
.pt25 {
  padding-top: 25rpx;
}
.pr25 {
  padding-right: 25rpx;
}
.pb25 {
  padding-bottom: 25rpx;
}
.pl25 {
  padding-left: 25rpx;
}

.p30 {
  padding: 30rpx;
}
.pt30 {
  padding-top: 30rpx;
}
.pr30 {
  padding-right: 30rpx;
}
.pb30 {
  padding-bottom: 30rpx;
}
.pl30 {
  padding-left: 30rpx;
}

.p35 {
  padding: 35rpx;
}
.pt35 {
  padding-top: 35rpx;
}
.pr35 {
  padding-right: 35rpx;
}
.pb35 {
  padding-bottom: 35rpx;
}
.pl35 {
  padding-left: 35rpx;
}

.p40 {
  padding: 40rpx;
}
.pt40 {
  padding-top: 40rpx;
}
.pr40 {
  padding-right: 40rpx;
}
.pb40 {
  padding-bottom: 40rpx;
}
.pl40 {
  padding-left: 40rpx;
}

.p44 {
  padding: 44rpx;
}
.pt45 {
  padding-top: 45rpx;
}
.pr45 {
  padding-right: 45rpx;
}
.pb45 {
  padding-bottom: 45rpx;
}
.pl45 {
  padding-left: 45rpx;
}

.p50 {
  padding: 50rpx;
}
.pt50 {
  padding-top: 50rpx;
}
.pr50 {
  padding-right: 50rpx;
}
.pb50 {
  padding-bottom: 50rpx;
}
.pl50 {
  padding-left: 50rpx;
}
