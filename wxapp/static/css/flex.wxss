.flex {
  /* 转为弹性盒模型  */
  display: flex;
}
.flex_wrap {
  /* 转为弹性盒模型并自动换行  */
  display: flex;
  flex-wrap: wrap;
}

.flex_b {
  /* 垂直底部对齐  */
  display: flex;
  align-items: flex-end;
}
.flex_c_end {
  display: flex;
  justify-content: flex-end;
}
.flex_tb {
  /* 垂直两端对齐  */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.flex_tb_c {
  /* 多行垂直两端对齐，水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.flex_line {
  /* 多行垂直 */
  display: flex;
  flex-direction: column;
}
.flex_line_c {
  /* 多行垂直水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
}
.flex_line_c_m {
  /* 多行垂直居中，水平居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.flex_line_end {
  /* 多行垂直起点在下沿 */
  display: flex;
  flex-direction: column-reverse;
}
.flex_lr {
  /* 水平两端对齐，剩余空间平均分布 */
  display: flex;
  justify-content: space-between;
}

.flex_lr_m {
  /* 水平两端对齐，剩余空间平均分布，垂直居中 */
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex_c_m {
  /* 垂直水平居中 */
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex_c {
  /* 水平居中 */
  display: flex;
  justify-content: center;
}

.flex_m {
  /* 垂直居中 */
  display: flex;
  align-items: center;
}

.flex_l_m {
  /* 垂直居中 */
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.flex_nosize {
  /* 子元素不自动 */
  flex-shrink: 0;
}

.flex_autosize {
  /* 子元素自动宽度 */
  flex-grow: 1;
}

.flex_inline {
  /* 转为行内弹性盒模型  */
  display: inline-flex;
}

.flex0 {
  flex: 0;
  min-width: 0;
}
.flex1 {
  flex: 1;
  min-width: 0;
}
