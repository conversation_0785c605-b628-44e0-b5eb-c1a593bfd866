{"name": "miniapp", "version": "1.0.0", "description": "", "main": "app.js", "dependencies": {"@vant/weapp": "^1.10.20"}, "devDependencies": {"babel-eslint": "^10.1.0", "eslint": "^7.26.0", "eslint-plugin-html": "^6.1.2"}, "scripts": {"lint": "eslint --fix ./", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "********************:frontend/patient-miniapp.git"}, "eslintIgnore": ["**/*wxml", "miniprogram_npm/**"], "author": "", "license": "ISC"}