Page {
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
}

.service-record-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 主tab栏样式 */
.main-tabs {
  position: fixed;
  left: 0;
  right: 0;
  height: 88rpx;
  background-color: #fff;
  display: flex;
  border-bottom: 2rpx solid #f0f0f0;
  z-index: 999;
}

.main-tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.main-tab-text {
  font-size: 32rpx;
  color: #666;
  transition: color 0.3s;
}

.main-tab-item.active .main-tab-text {
  color: #367DFF;
  font-weight: bold;
}

.main-tab-line {
  position: absolute;
  bottom: 0;
  width: 60rpx;
  height: 6rpx;
  background-color: #367DFF;
  border-radius: 3rpx;
}

/* tab内容区域 */
.tab-content {
  flex: 1;
  padding-top: 85rpx;
  overflow: hidden;
}

/* 问诊子tab栏样式 */
.consult-tabs {
  position: fixed;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  z-index: 998;
  display: flex;
  justify-content: space-between;
  background-color: #f5f5f5;
}

.consult-tabs-scroll {
  height: 80rpx;
  white-space: nowrap;
}

.consult-tab-item {
  /* padding: 15rpx 36rpx; */
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  width: 158rpx;
  height: 60rpx;
  background: #EEEEEE;
  border-radius: 30rpx;
}

.consult-tab-item.active {
  background: #DEEAFF;
}

.consult-tab-item.active .consult-tab-text {
  font-weight: bold;
  font-size: 28rpx;
  color: #367DFF;
}

/* 问诊记录滚动容器 */
.consult-scroll-container {
  flex: 1;
  height: 100%;
  box-sizing: border-box;
  padding-bottom: 20rpx;
}

/* 处方记录滚动容器 */
.prescription-list {
  flex: 1;
  height: 100%;
  box-sizing: border-box;
  padding: 0 30rpx 50rpx 30rpx;
}

/* 问诊记录列表样式 */
.order_list {
  padding-top: 60rpx;
  padding-bottom: 60rpx;
}

/* 补充样式类 */
.w40 {
  width: 40rpx;
}

.h40 {
  height: 40rpx;
}

.ml16 {
  margin-left: 16rpx;
}

.flex_jss {
  display: flex;
  justify-content: space-between;
}
.color-danger-pay {
  color: #F05542;
}

.color-primary-btn {
  color: #367DFF;
}

.mr15 {
  margin-right: 15rpx;
}

.zx999 {
  z-index: 999;
}

.fixed {
  position: fixed;
}

.no_msg_box {
  padding-top: 200rpx;
}

.no_msg {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

/* 加载更多提示 */
.load-more-tip {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  margin-bottom: 40rpx;
}

/* 药品容器样式 */
.medicine-container {
  height: 100%;
  position: relative;
}

.web-view-placeholder {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fff;
}

.placeholder-text {
  margin-top: 20rpx;
  font-size: 28rpx;
  color: #666;
}

.placeholder-desc {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}

/* 处方记录列表样式 */

.prescription-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx 0;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.c367DFF {
  color: #367DFF;
}

/* 药品项样式 */
.medicine-item {
  /* background-color: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #367DFF;
  margin-bottom: 15rpx;
  padding: 15rpx 20rpx; */
  transition: all 0.3s ease;
}

.medicine-item:last-child {
  margin-bottom: 0;
}

.medicine-item:hover {
  background-color: #f0f2f5;
}

.medicine-name {
  font-size: 28rpx;
  font-weight: 400;
  color: #333;
  margin-bottom: 8rpx;
}

.medicine-usage,
.medicine-quantity {
  font-size: 22rpx;
  color: #999;
  margin-top: 5rpx;
  line-height: 1.4;
}

.prescription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.prescription-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.prescription-date {
  font-size: 24rpx;
  color: #999;
}

.prescription-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.prescription-doctor,
.prescription-medicines {
  font-size: 28rpx;
  color: #666;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 页面顶部距离调整 */
.tab-content {
  padding-top: 88rpx; /* 主tab高度 */
  height: calc(100vh - 88rpx);
}

/* 问诊tab特殊处理，有子tab */
.consult-tab-content {
  padding-top: 0; /* 子tab是fixed定位，不需要padding */
  height: 100vh;
}

/* 滚动区域高度调整 */
.prescription-list {
  height: calc(100vh - 220rpx);
  padding-top: 20rpx;
}

/* 药品WebView高度调整 */
.medicine-container {
  height: calc(100vh - 220rpx);
}

/* 适配不同屏幕 */
@media (max-width: 375px) {
  .consult-item {
    padding: 24rpx;
  }
  
  .action-btn {
    padding: 12rpx 24rpx;
    font-size: 26rpx;
    min-width: 100rpx;
  }
  
  .main-tab-text {
    font-size: 30rpx;
  }
  
  .consult-tab-text {
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
  }
} 