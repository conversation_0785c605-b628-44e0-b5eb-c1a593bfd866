const app = getApp()
const api = require('../../config/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    ...app.globalData,
    statusBarHeight: null,
    backgroundColor: '#fff',
    navTitle: '服务记录',
    isBack: true,

    // tab相关
    mainTabList: ['问诊', '处方', '药品'],
    mainTabIndex: 0,

    // 问诊子tab相关
    consultTabList: ['全部', '进行中', '已完成', '已取消'],
    consultTabIndex: 0,

    // 数据相关
    consultList: [], // 问诊记录列表
    medicineWebViewUrl: '', // 药品h5页面url
    prescriptionList: [], // 处方记录列表

    // 加载状态
    loading: false,
    refreshing: false,

    // 分页
    consultPage: 1,
    prescriptionPage: 1,
    hasMoreConsult: true,
    hasMorePrescription: true,

    // 静态资源
    static: {
      nomes: api.ImgUrl + 'images/nomes.png',
      ic_doctor_video: api.ImgUrl + 'images/ic_doctor_video.png',
      ic_doctor_image: api.ImgUrl + 'images/ic_doctor_image.png',
      ic_order_video: api.ImgUrl + 'images/video_consult/<EMAIL>', // 视频icon
      ic_order_text: api.ImgUrl + 'images/video_consult/<EMAIL>' // 图文问诊icon
    }
  },

  onLoad(options) {
    console.log('服务记录页面加载', options)
    this.setData({
      statusBarHeight: app.globalData.statusBarHeight
    })

    // 初始化数据
    this.initData()
  },

  onShow() {
    // 页面显示时刷新数据
    if (this.data.mainTabIndex === 0) {
      this.refreshConsultData()
    } else if (this.data.mainTabIndex === 1) {
      this.refreshPrescriptionData()
    }
  },

  /**
   * 初始化数据
   */
  initData() {
    // 初始化问诊记录
    this.loadConsultData()

    // 初始化药品h5页面url
    this.initMedicineWebView()

    // 初始化处方记录
    this.loadPrescriptionData()
  },

  /**
   * 主tab切换
   */
  onMainTabChange(e) {
    const index = e.detail.index || e.currentTarget.dataset.index
    this.setData({
      mainTabIndex: index
    })

    // 根据选中的tab加载对应数据
    if (index === 0 && this.data.consultList.length === 0) {
      this.loadConsultData()
    } else if (index === 1) {
      // 切换到处方tab时，重置页码并加载数据
      this.setData({
        prescriptionPage: 1,
        hasMorePrescription: true
      })
      if (this.data.prescriptionList.length === 0) {
        this.loadPrescriptionData()
      }
    } else if (index === 2) {
      // 点击药品tab时，调用高济接口获取地址并跳转
      this.handleMedicineTabClick()
    }
  },

  /**
   * 问诊子tab切换
   */
  onConsultTabChange(e) {
    const index = e.detail.index || e.currentTarget.dataset.index
    this.setData({
      consultTabIndex: index,
      consultPage: 1,
      hasMoreConsult: true
    })
    this.loadConsultData()
  },

  /**
   * 加载问诊记录数据
   */
  async loadConsultData() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const status = this.getConsultStatus()
      const res = await util.request(api.consultOrderList, {
        page: this.data.consultPage, // 页码
        num: 10, // 每页数量
        consultStatus: status // 状态
      }, 'GET', '2')

      if (res.data.code === 0) {
        const newList = res.data.data?.result || []
        this.setData({
          consultList: this.data.consultPage === 1 ? newList : this.data.consultList.concat(newList),
          hasMoreConsult: newList.length === 10,
          consultPage: this.data.consultPage + 1
        })
      } else {
        console.error('获取问诊记录失败', res.data.msg)
        util.showToast({
          title: res.data.msg || '获取数据失败',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error('获取问诊记录失败', err)
      util.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false, refreshing: false })
    }
  },

  /**
   * 刷新问诊数据
   */
  refreshConsultData() {
    this.setData({
      consultPage: 1,
      hasMoreConsult: true,
      refreshing: true
    })
    this.loadConsultData()
  },

  /**
   * 加载更多问诊数据
   */
  loadMoreConsult() {
    if (this.data.hasMoreConsult && !this.data.loading) {
      this.loadConsultData()
    }
  },

  /**
   * 获取问诊状态值
   */
  getConsultStatus() {
    const statusMap = {
      0: null, // 全部
      1: 2, // 进行中
      2: 3, // 已完成
      3: 4 // 已取消
    }
    return statusMap[this.data.consultTabIndex]
  },

  /**
   * 处理药品tab点击事件
   */
  async handleMedicineTabClick() {
    try {
      // 检查登录状态
      const userInfo = wx.getStorageSync('userInfo')
      if (!userInfo || !userInfo.token) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        // 跳转到登录页面
        wx.navigateTo({
          url: '/pages/auth/login/login'
        })
        return
      }

      util.showLoading({
        title: '获取地址中...',
        mask: true
      })

      // 调用接口获取高济药品订单H5地址
      const { data } = await util.request(api.getMedicalOrderUrl, {}, 'post', 2)

      console.log('药品订单接口原始响应:', data)
      console.log('格式化后的数据:', data.data)

      util.hideLoading()

      if (data.code === 0 && data.data && data.data.jumpUrl) {
        // 跳转到高济药品订单H5页面
        wx.navigateTo({
          url: `/pages/webView/index?url=${encodeURIComponent(data.data.jumpUrl)}`
        })
      } else {
        // 如果接口失败，降级到原有的WebView方式
        console.log('获取高济H5地址失败，降级到原有WebView方式')
        this.initMedicineWebView()
      }
    } catch (error) {
      console.error('药品订单接口调用失败:', error)
      util.hideLoading()

      // 网络异常时，降级到原有的WebView方式
      console.log('网络异常，降级到原有WebView方式')
      this.initMedicineWebView()
    }
  },

  /**
   * 初始化药品WebView（降级方案）
   */
  initMedicineWebView() {
    // 这里设置药品h5页面的url，可以根据实际需求调整
    // 使用H5免登录token生成完整的药品页面URL
    try {
      const baseUrl = api.WebViewUrl || api.ImgUrl || ''
      const userId = app.globalData.userInfo?.userId || ''
      const medicineUrl = `${baseUrl}/medicine/list?userId=${userId}&from=miniapp`

      this.setData({
        medicineWebViewUrl: medicineUrl
      })
    } catch (err) {
      console.error('初始化药品WebView失败', err)
      // 如果出错，可以设置一个默认的URL或显示错误提示
      this.setData({
        medicineWebViewUrl: ''
      })
    }
  },

  /**
   * 加载处方记录数据
   */
  async loadPrescriptionData() {
    if (this.data.loading) return

    this.setData({ loading: true })

    try {
      const res = await util.request(api.prescriptionList, {
        page: this.data.prescriptionPage,
        num: 10
      }, 'GET', '2')

      console.log('处方记录接口响应:', res.data)

      if (res.data.code === 0) {
        // 根据实际接口返回的数据结构调整
        const newList = res.data.data?.list || res.data.data?.result || res.data.data || []
        console.log('处方记录数据:', newList)

        this.setData({
          prescriptionList: this.data.prescriptionPage === 1 ? newList : this.data.prescriptionList.concat(newList),
          hasMorePrescription: newList.length === 10,
          prescriptionPage: this.data.prescriptionPage + 1
        })
      } else {
        console.error('获取处方记录失败', res.data.msg)
        util.showToast({
          title: res.data.msg || '获取数据失败',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error('获取处方记录失败', err)
      util.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      })
    } finally {
      this.setData({
        loading: false,
        refreshing: false // 确保刷新状态被重置
      })
    }
  },

  /**
   * 刷新处方数据
   */
  refreshPrescriptionData() {
    console.log('刷新处方数据')
    this.setData({
      prescriptionPage: 1,
      hasMorePrescription: true,
      refreshing: true
    })
    this.loadPrescriptionData()
  },

  /**
   * 加载更多处方数据
   */
  loadMorePrescription() {
    if (this.data.hasMorePrescription && !this.data.loading) {
      this.loadPrescriptionData()
    }
  },

  /**
   * 问诊记录点击事件
   */
  onConsultItemClick(e) {
    console.log(e)
    const { ordersn } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/consult/order/detail/index?orderSn=${ordersn}`
    })
  },

  /**
   * 处方记录点击事件
   */
  onPrescriptionItemClick(e) {
    const item = e.currentTarget.dataset.item
    if (!item) return

    wx.navigateTo({
      url: `/pages/recipeDetail/recipeDetail?id=${item.prescriptionId}`
    })
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    console.log('下拉刷新，当前tab:', this.data.mainTabIndex)

    if (this.data.mainTabIndex === 0) {
      // 问诊tab
      this.refreshConsultData()
    } else if (this.data.mainTabIndex === 1) {
      // 处方tab
      this.refreshPrescriptionData()
    } else if (this.data.mainTabIndex === 2) {
      // 药品tab - 不需要刷新，因为会跳转到H5页面
      wx.stopPullDownRefresh()
      return
    }

    setTimeout(() => {
      wx.stopPullDownRefresh()
    }, 1500)
  },

  /**
   * 上拉加载更多（页面级别的，现在由scroll-view处理）
   */
  onReachBottom() {
    // 现在由各自的scroll-view处理滚动到底部事件
    // 保留此方法以防需要
  },

  /**
   * WebView消息处理
   */
  onWebViewMessage(e) {
    console.log('WebView消息:', e.detail.data)
    // 处理来自药品h5页面的消息
  },

  /**
   * 处理操作按钮点击事件
   */
  onActionClick(e) {
    const action = e.currentTarget.dataset.action
    const item = e.currentTarget.dataset.item

    console.log('按钮点击事件:', { action, item })

    switch (action) {
      case 'call_doctor':
        // 呼叫医生（视频问诊-待接诊）- 调用视频拨号接口
        this.callVideoDoctor(item)
        break
      case 'enter_room':
        // 进入诊室（视频问诊-进行中）- 跳转到视频通话页面
        wx.navigateTo({
          url: `/pages/meeting/meeting?roomID=${item?.roomId}&videoConsultId=${item?.id || item?.consultId}`
        })
        break
      case 'continue_consult':
        // 继续咨询（图文咨询）- 跳转到图文聊天页面
        wx.navigateTo({
          url: `/pages/consult/chat/chat?doctorId=${item?.doctorId}`
        })
        break
      case 'report':
        // 跳转到问诊报告页面 已完成状态的视频咨询页
        wx.navigateTo({
          url: `/pages_videoConsult/consultRoom/index?consultId=${item.id}&doctorId=${item.doctorId}&roomID=${item.roomID}&from=history`
        })
        break
      case 'prescription':
        // 查看处方
        wx.navigateTo({
          url: `/pages/recipeDetail/recipeDetail?recomId=${item?.recomId}`
        })
        break
      case 'evaluate':
        // 跳转到评价医生页面
        wx.navigateTo({
          url: `/pages/addEval/addEval?doctorId=${item.doctorId}&consultType=${item.consultType}&consultSessionId=${item.id}&type=2`
        })
        break
      case 'consult_again':
        // 再次咨询 - 根据咨询类型处理
        this.handleConsultAgain(item)
        break
      default:
        console.log('未知的按钮动作:', action)
        break
    }
  },

  /**
   * 处理再次咨询逻辑
   */
  handleConsultAgain(item) {
    console.log('处理再次咨询:', item)

    if (item.consultType === 2) {
      // 视频咨询：调用后端接口获取招商H5地址
      this.getConsultAgainUrl()
    } else {
      // 图文咨询：跳转到医生详情页面
      wx.navigateTo({
        url: `/pages/famousDoctorDetail/famousDoctorDetail?type=3&doctorId=${item.doctorId}`
      })
    }
  },

  /**
   * 呼叫医生（视频问诊-待接诊状态）
   */
  async callVideoDoctor(item) {
    try {
      // 显示loading状态
      wx.showLoading({
        title: '发起视频通话...',
        mask: true
      })

      // 构建接口参数
      const params = {
        patientId: app.globalData.userInfo.userId, // 患者ID
        videoConsultId: item.id // 视频咨询ID
      }

      console.log('调用视频拨号接口，参数:', params)

      // 验证必要参数
      if (!params.patientId) {
        wx.hideLoading()
        util.showToast({
          title: '用户信息异常，请重新登录',
          icon: 'none'
        })
        return
      }

      if (!params.videoConsultId) {
        wx.hideLoading()
        util.showToast({
          title: '问诊信息异常，请重试',
          icon: 'none'
        })
        return
      }

      // 调用视频拨号接口
      const res = await util.request(api.startVideoCall, params, 'post', '2')

      wx.hideLoading()

      if (res.data.code === 0) {
        console.log('视频拨号接口调用成功:', res.data.data)

        // 从接口响应中提取医生信息
        const responseData = res.data.data
        console.log('startVideoCall API 完整响应数据结构:', JSON.stringify(responseData, null, 2))

        // 提取医生信息，支持多种可能的字段名
        const doctorInfo = this.extractDoctorInfo(responseData)
        console.log('提取的医生信息:', doctorInfo)

        // 将医生信息存储到全局数据中，供呼叫医生页面使用
        if (doctorInfo && Object.keys(doctorInfo).length > 0) {
          if (app.globalData) {
            app.globalData.videoCallDoctorInfo = doctorInfo
            console.log('存储医生信息到全局数据:', doctorInfo)
          }
        }

        // 构建跳转URL，包含基础参数
        const callDoctorUrl = `/pages_videoConsult/callDoctor/index?videoConsultId=${params.videoConsultId}&patientId=${params.patientId}&roomId=${responseData.roomId}`

        // 添加其他必要参数
        let finalUrl = callDoctorUrl
        if (item.doctorId) {
          finalUrl += `&doctorId=${item.doctorId}`
        }
        if (item.consultId) {
          finalUrl += `&consultId=${item.consultId}`
        }
        if (item.inquirerId) {
          finalUrl += `&inquirerId=${item.inquirerId}`
        }

        // 添加医生信息参数到URL（作为备用方案）
        if (doctorInfo) {
          finalUrl = this.addDoctorInfoToUrl(finalUrl, doctorInfo)
        }

        console.log('跳转到呼叫医生页面:', finalUrl)

        wx.navigateTo({
          url: finalUrl
        })
      } else {
        console.error('视频拨号接口调用失败:', res.data.msg)
        util.showToast({
          title: res.data.msg || '发起视频通话失败',
          icon: 'none',
          duration: 3000
        })
      }

    } catch (err) {
      console.error('视频拨号接口调用异常:', err)
      wx.hideLoading()
      util.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none',
        duration: 3000
      })
    }
  },

  /**
   * 从API响应中提取医生信息
   * @param {Object} responseData API响应数据
   * @returns {Object} 提取的医生信息对象
   */
  extractDoctorInfo(responseData) {
    try {
      // 如果响应数据为空，返回空对象
      if (!responseData || typeof responseData !== 'object') {
        console.log('响应数据为空或格式不正确')
        return {}
      }

      // 提取医生信息，支持多种可能的字段名
      const doctorInfo = {
        // 医生头像 - 支持多种字段名
        avatar: responseData.doctorHeadUrl ||
                responseData.doctorPhoto ||
                responseData.headUrl ||
                responseData.avatar ||
                responseData.photo || '',

        // 医生姓名 - 支持多种字段名
        name: responseData.doctorName ||
              responseData.name ||
              responseData.realName || '医生',

        // 医生职称 - 支持多种字段名
        title: responseData.doctorTitle ||
               responseData.title ||
               responseData.position ||
               responseData.jobTitle || '',

        // 医生专长 - 支持多种字段名
        specialty: responseData.doctorExpertise ||
                   responseData.expertise ||
                   responseData.specialty ||
                   responseData.goodAt ||
                   responseData.speciality || '',

        // 其他可能有用的医生信息
        goodRate: responseData.goodRate || '',
        serviceCount: responseData.serviceCount || 0,
        department: responseData.department || responseData.deptName || '',
        hospital: responseData.hospital || responseData.hospitalName || ''
      }

      // 过滤掉空值，只保留有效的医生信息
      const filteredDoctorInfo = {}
      Object.keys(doctorInfo).forEach(key => {
        if (doctorInfo[key] !== '' && doctorInfo[key] !== null && doctorInfo[key] !== undefined) {
          filteredDoctorInfo[key] = doctorInfo[key]
        }
      })

      console.log('过滤后的医生信息:', filteredDoctorInfo)
      return filteredDoctorInfo

    } catch (error) {
      console.error('提取医生信息时发生错误:', error)
      return {}
    }
  },

  /**
   * 将医生信息添加到跳转URL中（作为备用传递方案）
   * @param {String} baseUrl 基础URL
   * @param {Object} doctorInfo 医生信息对象
   * @returns {String} 包含医生信息参数的完整URL
   */
  addDoctorInfoToUrl(baseUrl, doctorInfo) {
    try {
      let url = baseUrl

      // 只添加关键的医生信息到URL参数中，避免URL过长
      if (doctorInfo.name && doctorInfo.name !== '医生') {
        url += `&doctorName=${encodeURIComponent(doctorInfo.name)}`
      }

      if (doctorInfo.title) {
        url += `&doctorTitle=${encodeURIComponent(doctorInfo.title)}`
      }

      if (doctorInfo.avatar) {
        url += `&doctorAvatar=${encodeURIComponent(doctorInfo.avatar)}`
      }

      if (doctorInfo.specialty) {
        // 限制专长描述长度，避免URL过长
        const shortSpecialty = doctorInfo.specialty.length > 50
          ? doctorInfo.specialty.substring(0, 50) + '...'
          : doctorInfo.specialty
        url += `&doctorSpecialty=${encodeURIComponent(shortSpecialty)}`
      }

      return url

    } catch (error) {
      console.error('添加医生信息到URL时发生错误:', error)
      return baseUrl // 如果出错，返回原始URL
    }
  },

  /**
   * 获取再次咨询的H5地址
   */
  async getConsultAgainUrl() {
    try {
      util.showToast({
        title: '加载中...',
        icon: 'loading'
      })

      const params = {
        patientId: app.globalData.userInfo.userId
      }

      console.log('获取再次咨询H5地址参数:', params)

      // 调用后端接口获取招商H5地址
      const { data } = await util.request(api.getVideoConsultUrl, params, 'get')

      util.hideToast()

      if (data.code === 0) {
        const h5Url = data.data
        if (h5Url) {
          console.log('跳转到招商H5地址:', h5Url)
          // 跳转到H5页面
          wx.navigateTo({
            url: `/pages/webView/index?url=${encodeURIComponent(h5Url)}`
          })
        } else {
          util.showToast({
            title: '获取咨询地址失败',
            icon: 'none'
          })
        }
      } else {
        util.showToast({
          title: data.msg || '获取咨询地址失败',
          icon: 'none'
        })
      }
    } catch (err) {
      console.error('获取再次咨询H5地址失败', err)
      util.hideToast()
      util.showToast({
        title: '网络异常，请稍后重试',
        icon: 'none'
      })
    }
  }
})
