/* 和缓服务页面样式 */
.service-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 150rpx; /* 为底部按钮留出空间 */
}

/* 背景图容器 */
.bg-container {
  width: 100%;
  /* 根据设计图的比例设置高度，假设是长图 */
  height: 1600rpx; /* 可以根据实际图片比例调整 */
  background-size: 100% auto;
  background-position: center top;
  background-repeat: no-repeat;
}

/* 立即问诊按钮容器 */
.consult-btn-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 40rpx;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom)); /* 适配iPhone底部安全区域 */
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95) 80%, rgba(255, 255, 255, 0));
  z-index: 100;
}

/* 立即问诊按钮 */
.consult-btn {
  width: 100%;
  height: 88rpx;
  background: #367DFF;
  border-radius: 8rpx;
  border: none;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
  transition: all 0.3s ease;
  -webkit-border-radius: 8rpx;
  -moz-border-radius: 8rpx;
  -ms-border-radius: 8rpx;
  -o-border-radius: 8rpx;
}

.consult-btn::after {
  border: none;
}

.consult-btn:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.4);
}