/* 呼叫医生页面样式 */
.call-container {
  width: 100%;
  height: 100vh;
  background: #000000;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 状态栏占位 */
.status-bar {
  width: 100%;
  background: transparent;
}

/* 医生信息区域 */
.doctor-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 60rpx 80rpx;
  margin-top: 60rpx;
}

.doctor-avatar {
  width: 308rpx;
  height: 308rpx;
  border-radius: 40rpx;
  overflow: hidden;
  margin-bottom: 32rpx;
  background: #EEE;
  -webkit-border-radius: 40rpx;
  -moz-border-radius: 40rpx;
  -ms-border-radius: 40rpx;
  -o-border-radius: 40rpx;
}

.doctor-avatar image {
  width: 100%;
  height: 100%;
}

.doctor-details {
  text-align: center;
}

.doctor-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 20rpx;
}

.doctor-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #cccccc;
  margin-top: 16rpx;
  flex-wrap: wrap;
}

.doctor-stats text {
  white-space: nowrap;
}

.stat-item {
  color: #cccccc;
}

.stat-divider {
  margin: 0 20rpx;
  color: #666666;
}

/* 医生专长介绍 */
.doctor-specialty {
  padding: 0 60rpx;
  margin-bottom: 40rpx;
}

.doctor-specialty text {
  display: block;
  font-size: 30rpx;
  color: #FFFFFF;
  line-height: 1.6;
  text-align: center;
  background: rgba(255, 255, 255, 0.14);
  padding: 30rpx;
  border-radius: 16rpx;
}

/* 呼叫状态区域 */
.call-status {
  /* flex: 1; */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 240rpx 60rpx 122rpx;
}

.status-calling text,
.status-ended text {
  font-size: 30rpx;
  color: #ffffff;
  text-align: center;
}

.status-calling text {
  animation: breathing 2s ease-in-out infinite;
}

@keyframes breathing {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.status-ended text {
  color: #ff6666;
}

/* 控制按钮区域 */
.call-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 60rpx 120rpx;
}

.hang-up-btn {
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 71, 87, 0.3);
  transition: all 0.3s ease;
}

.hang-up-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.5);
}

.hang-up-btn image {
  width: 140rpx;
  height: 140rpx;
}

.hang-up-text {
  font-size: 28rpx;
  color: #ffffff;
  text-align: center;
} 