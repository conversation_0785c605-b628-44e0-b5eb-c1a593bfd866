<view class="service-page">
 <navbar isBack="{{isBack}}" home="{{!isBack}}" backgroundColor="{{backgroundColor}}" navTitle="{{navTitle}}"></navbar>
  <!-- 背景图区域 -->
  <view class="bg-container" style="background-image: url('{{bg_yaoying}}');">
    <!-- 背景图内容区域 -->
  </view>

  <!-- 立即问诊按钮 -->
  <view class="consult-btn-container">
    <button class="consult-btn" bindtap="handleConsult">
      立即问诊
    </button>
  </view>
</view>
