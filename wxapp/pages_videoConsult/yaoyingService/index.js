const api = require('../../config/api.js')
const util = require('../../utils/util.js')

Page({
  data: {
    isBack: true,
    backgroundColor: '#fff',
    navTitle: '服务中心',
    bg_yaoying: api.ImgUrl + 'images/video_consult/<EMAIL>',
    // 写死的目标小程序参数
    targetAppId: 'wxe929c9eabec712f1',
    targetPath: '',
    // 从页面参数中获取的动态参数
    cignaUId: '',
    language: '',
    cignaPackageCode: '',
    cignaCustomerNo: '',
    cignaSubOrderNo: ''
  },

  onLoad(options) {
    console.log('耀影服务介绍页面加载', options)

    // 解析页面参数并构建跳转路径
    this.parseParameters(options)
  },

  onShow() {
    // 页面显示时的逻辑
  },

  /**
   * 解析页面参数并构建目标小程序跳转路径
   * @param {Object} options 页面参数
   */
  parseParameters(options) {
    try {
      // 从页面参数中获取5个动态参数
      const cignaUId = options.cignaUId || 'xxxxx'
      const language = options.language || 'xxxxx'
      const cignaPackageCode = options.cignaPackageCode || 'xxxxx'
      const cignaCustomerNo = options.cignaCustomerNo || 'xxxxx'
      const cignaSubOrderNo = options.cignaSubOrderNo || 'xxxxx'

      // 构建完整的目标路径
      const basePath = '/pages/home/<USER>'
      const dynamicParams = `&cignaUId=${cignaUId}&language=${language}&cignaPackageCode=${cignaPackageCode}&cignaCustomerNo=${cignaCustomerNo}&cignaSubOrderNo=${cignaSubOrderNo}`
      const fullPath = basePath + dynamicParams

      // 更新数据
      this.setData({
        targetPath: fullPath,
        cignaUId: cignaUId,
        language: language,
        cignaPackageCode: cignaPackageCode,
        cignaCustomerNo: cignaCustomerNo,
        cignaSubOrderNo: cignaSubOrderNo
      })

      console.log('构建的目标路径:', fullPath)
      console.log('动态参数:', {
        cignaUId,
        language,
        cignaPackageCode,
        cignaCustomerNo,
        cignaSubOrderNo
      })

    } catch (error) {
      console.error('解析参数失败:', error)
      util.showToast({
        title: '参数解析失败',
        icon: 'none'
      })
    }
  },

  /**
   * 参数验证
   * @returns {Boolean} 验证结果
   */
  validateParameters() {
    if (!this.data.targetAppId) {
      util.showToast({
        title: '缺少目标小程序ID',
        icon: 'none'
      })
      return false
    }

    if (!this.data.targetPath) {
      util.showToast({
        title: '缺少目标页面路径',
        icon: 'none'
      })
      return false
    }

    return true
  },

  /**
   * 立即问诊按钮点击事件 - 跳转到目标小程序
   */
  handleConsult() {
    console.log('点击立即问诊按钮')
    console.log('跳转参数:', {
      appId: this.data.targetAppId,
      path: this.data.targetPath,
      cignaUId: this.data.cignaUId,
      language: this.data.language,
      cignaPackageCode: this.data.cignaPackageCode,
      cignaCustomerNo: this.data.cignaCustomerNo,
      cignaSubOrderNo: this.data.cignaSubOrderNo
    })

    // 验证参数
    if (!this.validateParameters()) {
      console.log('参数验证失败')
      return
    }

    // 显示加载提示
    util.showToast({
      title: '正在跳转...',
      icon: 'loading'
    })

    // 跳转到目标小程序
    wx.navigateToMiniProgram({
      appId: this.data.targetAppId,
      path: this.data.targetPath,
      envVersion: 'release', // 正式版
      success: (res) => {
        console.log('跳转到目标小程序成功:', res)
        util.hideToast()
      },
      fail: (err) => {
        console.error('跳转到目标小程序失败:', err)
        util.hideToast()

        // 根据错误类型显示不同的提示信息
        let errorMsg = '跳转失败，请稍后重试'
        if (err.errMsg) {
          if (err.errMsg.includes('cancel')) {
            errorMsg = '用户取消跳转'
          } else if (err.errMsg.includes('appId')) {
            errorMsg = '目标小程序不存在或未发布'
          } else if (err.errMsg.includes('path')) {
            errorMsg = '目标页面路径错误'
          }
        }

        util.showToast({
          title: errorMsg,
          icon: 'none',
          duration: 3000
        })
      }
    })
  },

  /**
   * 分享页面
   */
  onShareAppMessage() {
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
  }
})
