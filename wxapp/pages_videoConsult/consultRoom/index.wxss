/* 导入图文问诊的样式 */
@import "../../pages/consult/chat/chat.wxss";

Page {
  background-color: var(--bgColor);
  color: #000;
  font-size: 30rpx;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.chating-wrapper {
  width: 100%;
  min-height: 100%;
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

/*聊天记录  */
.record-wrapper {
  width: 100%;
  position: fixed;
  left: 0;
  top: 380rpx; /* 调整位置，为就诊人信息区域留出空间 */
  bottom: 30rpx; /* 视频咨询室没有输入框，底部距离更小 */
}

/* 就诊人信息框 */
.patient-info-box {
  width: 100%;
  padding: 30rpx;
  position: fixed;
  top: 120rpx;
  left: 0;
  /* z-index: 9999; */
  border-bottom: 2rpx solid #f5f5f5;
}

.patient-info-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.patient-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.required-mark {
  color: #ff4444;
  font-size: 32rpx;
  margin-left: 8rpx;
}

/* 就诊人滑动区域 */
.patient-scroll {
  height: 120rpx;
  margin-bottom: 24rpx;
  white-space: nowrap;
  scroll-behavior: smooth;
}

.patient-scroll-content {
  display: flex;
  padding: 0 10rpx;
}

.patient-item {
  display: inline-block;
  margin-right: 20rpx;
  transition: all 0.3s ease;
}

.patient-item:last-child {
  margin-right: 0;
}

.patient-content {
  width: 282rpx;
  height: 112rpx;
  background: #F8F8F8;
  border-radius: 12rpx;
  border: 2rpx solid #E4E4E4;
  padding: 20rpx;
  box-sizing: border-box;
  transition: all 0.3s ease;
}

.patient-item.active .patient-content {
  border-color: #367DFF;
  background: #F0F7FF;
}


.patient-main-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.patient-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.edit-icon {
  width: 32rpx;
  height: 32rpx;
}

.patient-detail-info {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.patient-detail-supplement {
  font-weight: 400;
  font-size: 22rpx;
  color: #FF6666;
}

.patient-gender,
.patient-age {
  font-size: 22rpx;
  color: #A8A8A8;
}

.patient-relation {
  color: #A8A8A8;
  font-size: 22rpx;
}

/* 呼叫视频医生按钮 */
.call-doctor-btn {
  width: 100%;
  height: 88rpx;
  background: #367DFF;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 24rpx;
  -webkit-border-radius: 8rpx;
  -moz-border-radius: 8rpx;
  -ms-border-radius: 8rpx;
  -o-border-radius: 8rpx;
}

.call-doctor-btn:active {
  opacity: 0.8;
}

.call-btn-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 待接诊状态下的呼叫医生按钮容器 */
.waiting-call-doctor-box {
  width: 100%;
  position: fixed;
  left: 0;
  padding: 30rpx;
  box-sizing: border-box;
  z-index: 9998;
}

/* 待接诊状态下的呼叫医生按钮 */
.waiting-call-doctor-btn {
  width: 100%;
  height: 88rpx;
  background: #367DFF;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-border-radius: 8rpx;
  -moz-border-radius: 8rpx;
  -ms-border-radius: 8rpx;
  -o-border-radius: 8rpx;
}

.waiting-call-doctor-btn:active {
  opacity: 0.8;
}

.waiting-call-btn-text {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
}

/* 医生信息框 */
.dob-box {
  width: 100%;
  padding: 10px 3%;
  position: fixed;
  top: 320rpx;
  left: 0;
}

.doc-photo image {
  width: 42px;
  height: 42px;
  border-radius: 50%;
}

/* 服务记录按钮 */
.service-record-btn {
  position: fixed;
  right: 20rpx;
  bottom: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 99;
}

.service-record-btn image {
  width: 104rpx;
  height: 104rpx;
}

.service-record-btn:active {
  opacity: 0.8;
  transform: scale(0.95);
}

/* 时间文本 */
.timeText {
  text-align: center;
  color: #999999;
  font-size: 26rpx;
  padding: 30rpx 0;
}

/* 系统消息样式 */
.systemChat {
  color: #333;
  font-size: 32rpx;
}

.recipel {
  background-color: #fff;
  border-radius: 4rpx;
}

.recipelMess {
  width: 490rpx;
  border-radius: 8rpx;
}

.recipelMess .title {
  height: 80rpx;
  border-radius: 8rpx 8rpx 0 0;
  font-weight: 600;
  position: relative;
}

.bg_image {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.recipelMess .title2 {
  height: 80rpx;
  border-radius: 8rpx 8rpx 0 0;
  font-weight: 600;
  position: relative;
}

.recipelMess .drug {
  padding-bottom: 10rpx;
}

.recipelMess .durguse {
  padding-top: 10rpx;
}

/* 评价样式 */
.evaluate {
  padding: 30rpx 90rpx;
  border-radius: 16rpx;
}

.evaluate-title {
  height: 90rpx;
  background: linear-gradient(
    180deg,
    rgba(74, 163, 255, 0.3) 0%,
    rgba(40, 147, 255, 0) 100%
  );
  border-radius: 16rpx 16rpx 0px 0px;
}

/* 富文本样式 */
.rich-text {
  text-decoration: underline;
  color: var(--themeColor);
}

/* 无消息提示 */
.no_msg_box {
  height: 100%;
  padding-top: 200rpx;
}

.no_msg {
  width: 200rpx;
  height: 200rpx;
}

/* 小助理消息样式修复 */
.flex-column {
  display: flex;
  flex-direction: column;
  flex: 1;
}

/* 确保小助理头像不被挤压 */
.record-chatting-item.other .record-chatting-item-img {
  flex-shrink: 0;
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
}