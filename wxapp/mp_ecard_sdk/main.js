import validate from"./utils/validate";import Log from"./constants/log";function initEid(e="https://eid.faceid.qq.com"){wx.eidBaseUrl=e,wx.onAppShow(e=>{console.log("!!!!!!监听onshow事件",e);const{scene:o}=e;if(1038!==o)return;const{referrerInfo:t}=e,{appId:n,extraData:r}=t;if("wx0e2cb0b052a91c92"!==n||!r)return;const{verifyDone:i,token:a}=r;i&&wx.handleEidVerifyDone?wx.eidTokenToCallback&&wx.eidTokenToCallback===a&&(wx.eidTokenToCallback="",wx.reportLogToEid({token:a,event:Log.navigateBackFromEid,errMsg:`从EID核身完成返回，token:${a},verifyDone:${i}`}),console.log("!!!!!!执行回调"),wx.handleEidVerifyDone(r)):wx.reportLogToEid({token:a,event:Log.navigateBackFromEidFail,errMsg:`核验未完成或者没有处理核验完成的函数，token:${a},verifyDone:${i}`})});const o=wx.getSystemInfoSync(),{version:t}=o;wx.reportLogToEid=function(e){const{token:o="",event:n="",errCode:r="",errMsg:i="",data:a=""}=e,s=new Date,d={Token:o,SourceType:"mp_sdk",SourceVersion:"1.0.4",EnvVersion:t,Timestamp:s.getTime(),Event:n,ErrorCode:"number"==typeof r?r.toString():r,ErrorMsg:i,Data:a};console.log("开始上报日志：",d),wx.request({url:`${wx.eidBaseUrl}/api/common/ReportEvent`,method:"POST",data:d,success(e){console.log("上报日志完成：","payload:",d,"res:",e)}})}}function startEid(e){const{data:o,verifyDoneCallback:t}=e;if(!o||!t)return wx.reportLogToEid({token:n,event:Log.startEidFail,errMsg:"传入的参数有误"}),void wx.showModal({title:"提示",content:"传入的参数有误",showCancel:!1});const{token:n}=o;if(!validate(n,"token"))return wx.reportLogToEid({token:n,event:Log.startEidFail,errMsg:`传入的token有误，token:${n}`}),void wx.showModal({title:"提示",content:"传入的token有误",showCancel:!1});wx.handleEidVerifyDone=(e=>{const{token:o}=e;wx.navigateBack({success(){wx.reportLogToEid({token:o,event:Log.EidVerifyDone,errMsg:`验证完成，token：${o}`}),t({token:o,verifyDone:!0})}})}),wx.navigateTo({url:`/mp_ecard_sdk/index/index?token=${n}`})}module.exports={initEid:initEid,startEid:startEid};