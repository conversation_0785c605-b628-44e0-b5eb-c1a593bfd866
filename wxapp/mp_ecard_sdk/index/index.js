import getParameterByName from"../utils/getParameterByName";import Log from"../constants/log";Component({options:{styleIsolation:"page-apply-shared"},data:{token:"",appName:"",isAgree:!!wx.getStorageSync("isAgree"),showAuth:!1,showWebView:!1,redirectUri:"",isNavigating:!1},methods:{onLoad(e){const{token:t}=e;this.setData({token:t,isAgree:!!wx.getStorageSync("isAgree"),redirectUri:`${wx.eidBaseUrl}/api/v1/Redirect?token=${t}`},()=>{this.setData({showWebView:!0}),this.getConfig(t)})},getConfig(e){const t=this;wx.request({url:`${wx.eidBaseUrl}/api/v1/GetConfig?token=${e}`,method:"GET",success(a){if(wx.reportLogToEid({token:e,event:Log.getConfig,errCode:a.data?a.data.ErrorCode:"",errMsg:a.data?a.data.ErrorMsg:""}),a.data&&0===a.data.ErrorCode){const e=a.data.Data.AppName;return void t.setData({appName:e})}const o=a.data.ErrorMsg;wx.showModal({title:"提示",content:o,showCancel:!1,success(){wx.navigateBack()}})},fail(t){console.log("网络失败，请点击重新尝试",t),wx.reportLogToEid({token:e,event:Log.getConfigFail,errCode:"",errMsg:t.errMsg}),wx.showModal({title:"提示",content:"网络失败，请点击重新尝试",showCancel:!1,success(){wx.navigateBack()}})}})},handleWebViewLoad(e){console.log("webview load",e.detail.src);const{token:t}=this.data,a=getParameterByName("success",e.detail.src);a&&this.setData({showWebView:!1,showAuth:!0}),wx.reportLogToEid({token:t,event:Log.webViewResult,errMsg:`success参数为${a}`})},handleWebViewError(e){console.log("webview error",e);this.setData({showWebView:!1,showAuth:!0});const{token:t}=this.data;wx.reportLogToEid({token:t,event:Log.webViewError,errMsg:e.detail.src})},changeAgree(){const e=!this.data.isAgree;this.setData({isAgree:e},()=>{wx.setStorageSync("isAgree",e)})},navigateToEid(){const{token:e}=this.data;this.setData({isNavigating:!0});const t=this;wx.navigateToMiniProgram({appId:"wx0e2cb0b052a91c92",path:"pages/huiyan/index",extraData:{useHuiyan:!0,huiyanToken:e},success(){wx.eidTokenToCallback=e},complete(a){t.setData({isNavigating:!1}),wx.reportLogToEid({token:e,event:Log.navigateToEid,errMsg:a.errMsg})}})}}});