# 互联网医院小程序项目

## 项目结构

```
├── .gitignore              # Git忽略文件配置
├── README.md              # 项目说明文档
└── wxapp/                 # 微信小程序主目录
    ├── .eslintrc.js       # ESLint配置文件
    ├── package.json       # 项目依赖配置
    ├── package-lock.json  # 依赖版本锁定文件
    ├── app.js            # 小程序入口文件
    ├── app.json          # 小程序全局配置
    ├── app.wxss          # 小程序全局样式
    ├── project.config.json    # 微信开发者工具配置
    ├── project.private.config.json  # 私有配置
    ├── sitemap.json      # 小程序搜索配置
    ├── pages/            # 页面目录
    ├── components/       # 组件目录
    ├── lib/              # 第三方库目录
    ├── utils/            # 工具函数目录
    ├── config/           # 配置文件目录
    ├── static/           # 静态资源目录
    └── miniprogram_npm/  # npm构建产物（自动生成，已忽略）
```

## 分包策略建议

### 推荐的分包结构

```
├── .gitignore              # Git忽略文件配置
├── README.md              # 项目说明文档
└── wxapp/                 # 微信小程序主目录
    ├── pages/             # 主包页面（TabBar页面 + 核心页面）
    │   ├── home/          # 首页（TabBar）
    │   ├── consult/index/ # 咨询首页（TabBar）
    │   ├── user/          # 我的（TabBar）
    │   ├── auth/login/    # 登录页面
    │   └── webView/       # 通用WebView
    ├── subPackages/       # 分包目录
    │   ├── videoConsult/  # 全科视频问诊分包
    │   │   ├── pages/
    │   │   │   ├── doctorList/     # 医生列表
    │   │   │   ├── doctorDetail/   # 医生详情
    │   │   │   ├── booking/        # 预约挂号
    │   │   │   ├── videoRoom/      # 视频通话室
    │   │   │   ├── consultRecord/  # 问诊记录
    │   │   │   └── prescription/   # 处方详情
    │   │   ├── components/         # 分包专用组件
    │   │   └── utils/             # 分包工具函数
    │   ├── pharmacy/      # 药房相关分包
    │   │   └── pages/
    │   │       ├── goods/
    │   │       ├── orderList/
    │   │       ├── confirmOrder/
    │   │       └── logistics/
    │   └── profile/       # 个人中心分包
    │       └── pages/
    │           ├── peopleContent/
    │           ├── address/
    │           └── evalList/
```

### app.json 配置示例

```json
{
  "pages": [
    "pages/home/<USER>",
    "pages/consult/index/index",
    "pages/user/user",
    "pages/auth/login/login",
    "pages/webView/index"
  ],
  "subPackages": [
    {
      "root": "subPackages/videoConsult",
      "name": "videoConsult",
      "pages": [
        "pages/doctorList/index",
        "pages/doctorDetail/index",
        "pages/booking/index",
        "pages/videoRoom/index",
        "pages/consultRecord/index",
        "pages/prescription/index"
      ]
    },
    {
      "root": "subPackages/pharmacy",
      "name": "pharmacy",
      "pages": [
        "pages/goods/goods",
        "pages/orderList/orderList",
        "pages/confirmOrder/confirmOrder",
        "pages/logistics/logistics"
      ]
    },
    {
      "root": "subPackages/profile",
      "name": "profile",
      "pages": [
        "pages/peopleContent/people/people",
        "pages/peopleContent/addPeople/addPeople",
        "pages/address/index",
        "pages/evalList/evalList"
      ]
    }
  ],
  "tabBar": {
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "首页"
      },
      {
        "pagePath": "pages/consult/index/index",
        "text": "咨询"
      },
      {
        "pagePath": "pages/user/user",
        "text": "我的"
      }
    ]
  }
}
```

## 开发说明

### 安装依赖

```bash
cd wxapp
npm install
```

### 代码检查

```bash
cd wxapp
npm run lint
```

### 构建npm包

在微信开发者工具中：
1. 点击 工具 -> 构建npm
2. 构建完成后会生成 `miniprogram_npm` 目录

### 分包跳转示例

```javascript
// 从主包跳转到分包
wx.navigateTo({
  url: '/subPackages/videoConsult/pages/doctorList/index'
})

// 从分包跳转到主包
wx.navigateTo({
  url: '/pages/home/<USER>'
})

// 从分包跳转到其他分包
wx.navigateTo({
  url: '/subPackages/pharmacy/pages/goods/goods'
})
```

### 注意事项

- `package.json` 位于 `wxapp` 目录下，这是微信小程序的标准做法
- `miniprogram_npm` 目录已添加到 `.gitignore` 中，无需提交到版本控制
- 使用 `@vant/weapp` 组件库，需要先构建npm包才能使用
- 分包与主包可以相互跳转，没有限制
- TabBar页面必须放在主包中
